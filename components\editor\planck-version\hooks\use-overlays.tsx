import { useState, useCallback } from "react";
import { Overlay, OverlayType, CaptionStyles, CaptionOverlay } from "../types";
import { defaultCaptionStyles } from "../components/overlays/captions/caption-settings";
import { getEffectiveFPS } from "../utils/fps-utils";

/**
 * Hook to manage overlay elements in the editor
 * Overlays can be text, videos, or sounds that are positioned on the timeline
 * @returns Object containing overlay state and management functions
 */
export const useOverlays = (initialOverlays?: Overlay[]) => {
  // Initialize with provided overlays or default overlays
  const [overlays, setOverlays] = useState<Overlay[]>(initialOverlays || []);

  // Tracks which overlay is currently selected for editing
  const [selectedOverlayId, setSelectedOverlayId] = useState<number | null>(
    null
  );

  /**
   * Updates properties of a specific overlay
   * Supports both direct property updates and functional updates
   * @example
   * // Direct update
   * changeOverlay(1, { width: 100 })
   * // Functional update
   * changeOverlay(1, (overlay) => ({ ...overlay, width: overlay.width + 10 }))
   */
  const changeOverlay = useCallback(
    (
      overlayId: number,
      updater: Partial<Overlay> | ((overlay: Overlay) => Overlay)
    ) => {
      setOverlays((prevOverlays) =>
        prevOverlays.map((overlay) => {
          if (overlay.id !== overlayId) return overlay;
          return typeof updater === "function"
            ? updater(overlay)
            : ({ ...overlay, ...updater } as Overlay);
        })
      );
    },
    []
  );

  /**
   * Adds a new overlay to the editor
   * Can accept an optional ID or automatically generates a new unique ID starting from 1
   * Deselects any currently selected overlay
   */
  const addOverlay = useCallback(
    (newOverlay: Omit<Overlay, "id">, optionalId?: number) => {
      let newId: number;
      setOverlays((prevOverlays) => {
        if (optionalId !== undefined) {
          // Use the provided ID if specified
          newId = optionalId;
        } else {
          // Generate new ID starting from 1
          newId =
            prevOverlays.length > 0
              ? Math.max(...prevOverlays.map((o) => o.id)) + 1
              : 1;
        }
        const overlayWithNewId = { ...newOverlay, id: newId } as Overlay;
        return [...prevOverlays, overlayWithNewId];
      });
      setSelectedOverlayId(newId!);
    },
    []
  );

  /**
   * Removes an overlay by its ID and clears the selection
   */
  const deleteOverlay = useCallback((id: number) => {
    // First find the overlay to be deleted for cleanup
    setOverlays((prevOverlays) => {
      const overlayToDelete = prevOverlays.find((overlay) => overlay.id === id);

      if (overlayToDelete) {
        // Clean up any blob URLs for audio overlays
        if (
          overlayToDelete.type === "sound" &&
          overlayToDelete.src &&
          overlayToDelete.src.startsWith("blob:")
        ) {
          try {
            URL.revokeObjectURL(overlayToDelete.src);
          } catch (error) {
            console.warn("Failed to cleanup blob URL in deleteOverlay:", error);
          }
        }
      }

      return prevOverlays.filter((overlay) => overlay.id !== id);
    });

    setSelectedOverlayId(null);
  }, []);

  /**
   * Removes all overlays on a specified row
   * @param row The row number to clear
   */
  const deleteOverlaysByRow = useCallback((row: number) => {
    setOverlays((prevOverlays) =>
      prevOverlays.filter((overlay) => overlay.row !== row)
    );
    setSelectedOverlayId(null);
  }, []);

  /**
   * Creates a copy of an existing overlay
   * The duplicated overlay is positioned immediately after the original in the timeline
   */
  const duplicateOverlay = useCallback((id: number) => {
    setOverlays((prevOverlays) => {
      const overlayToDuplicate = prevOverlays.find(
        (overlay) => overlay.id === id
      );
      if (!overlayToDuplicate) return prevOverlays;

      // 🔥 FIXED: Ensure duplicated IDs also start from 1 minimum
      const existingIds = prevOverlays.map((o) => o.id);
      const newId = Math.max(...existingIds, 0) + 1; // Ensure minimum of 1

      // Find any overlays that would overlap with the duplicated position
      const overlaysInRow = prevOverlays.filter(
        (o) => o.row === overlayToDuplicate.row && o.id !== id
      );

      // Calculate initial position for duplicate
      let newFrom =
        overlayToDuplicate.from + overlayToDuplicate.durationInFrames;

      // Check for overlaps and adjust position if needed
      let hasOverlap = true;
      while (hasOverlap) {
        hasOverlap = overlaysInRow.some((existingOverlay) => {
          const duplicateEnd = newFrom + overlayToDuplicate.durationInFrames;
          const existingEnd =
            existingOverlay.from + existingOverlay.durationInFrames;

          // Check for any overlap
          return (
            (newFrom >= existingOverlay.from && newFrom < existingEnd) ||
            (duplicateEnd > existingOverlay.from &&
              duplicateEnd <= existingEnd) ||
            (newFrom <= existingOverlay.from && duplicateEnd >= existingEnd)
          );
        });

        if (hasOverlap) {
          // If there's an overlap, try positioning after the last overlay in the row
          const lastOverlay = [...overlaysInRow].sort(
            (a, b) =>
              b.from + b.durationInFrames - (a.from + a.durationInFrames)
          )[0];
          newFrom = lastOverlay
            ? lastOverlay.from + lastOverlay.durationInFrames + 1
            : newFrom + 1;
        }
      }

      const duplicatedOverlay: Overlay = {
        ...overlayToDuplicate,
        id: newId,
        from: newFrom,
      };

      return [...prevOverlays, duplicatedOverlay];
    });
  }, []);

  /**
   * Splits an overlay into two separate overlays at a specified frame
   * Useful for creating cuts or transitions in video/audio content
   * @example
   * // Split an overlay at frame 100
   * splitOverlay(1, 100)
   * // Split with specific ID for the new segment
   * splitOverlay(1, 100, 2)
   */
  const splitOverlay = useCallback(
    (id: number, splitFrame: number, newSegmentId?: number) => {
      setOverlays((prevOverlays) => {
        const fps = getEffectiveFPS(prevOverlays); // Use actual video FPS
        const msPerFrame = 1000 / fps;

        const overlayToSplit = prevOverlays.find(
          (overlay) => overlay.id === id
        );
        if (!overlayToSplit) {
          return prevOverlays;
        }

        // Validate split point with tolerance for floating point precision
        const overlayStart = overlayToSplit.from;
        const overlayEnd =
          overlayToSplit.from + overlayToSplit.durationInFrames;
        const tolerance = 0.1; // Small tolerance for frame calculations

        if (
          splitFrame <= overlayStart + tolerance ||
          splitFrame >= overlayEnd - tolerance
        ) {
          return prevOverlays;
        }

        const firstPartDuration = splitFrame - overlayToSplit.from;
        const secondPartDuration =
          overlayToSplit.durationInFrames - firstPartDuration;

        // Use provided segment ID or generate new one
        let newId: number;
        if (newSegmentId !== undefined) {
          newId = newSegmentId;
        } else {
          // 🔥 FIXED: Ensure split IDs also start from 1 minimum
          const existingIds = prevOverlays.map((o) => o.id);
          newId = Math.max(...existingIds, 0) + 1; // Ensure minimum of 1
        }

        // Calculate start times for media overlays
        const secondHalfStartTime = calculateSecondHalfStartTime(
          overlayToSplit,
          firstPartDuration
        );

        // Create split overlays
        const [firstHalf, secondHalf] = createSplitOverlays(
          overlayToSplit,
          newId,
          splitFrame,
          firstPartDuration,
          secondPartDuration,
          secondHalfStartTime,
          fps
        );
        return prevOverlays
          .map((overlay) => (overlay.id === id ? firstHalf : overlay))
          .concat(secondHalf);
      });
    },
    []
  );

  const updateOverlayStyles = useCallback(
    (overlayId: number, styles: Partial<CaptionStyles>) => {
      changeOverlay(overlayId, (overlay) => {
        if (overlay.type !== OverlayType.CAPTION) return overlay;
        return {
          ...overlay,
          styles: {
            ...(overlay.styles || defaultCaptionStyles),
            ...styles,
          },
        };
      });
    },
    [changeOverlay]
  );

  const resetOverlays = useCallback(() => {
    setOverlays([]);
    setSelectedOverlayId(null);
  }, []);

  return {
    overlays,
    selectedOverlayId,
    setSelectedOverlayId,
    setOverlays,
    changeOverlay,
    addOverlay,
    deleteOverlay,
    deleteOverlaysByRow,
    duplicateOverlay,
    splitOverlay,
    updateOverlayStyles,
    resetOverlays,
  };
};

/**
 * Calculates the starting time for the second half of a split media overlay
 * For clips and sounds, we need to adjust their internal start times
 * to maintain continuity after the split
 */
const calculateSecondHalfStartTime = (
  overlay: Overlay,
  firstPartDuration: number
): number => {
  if (overlay.type === OverlayType.VIDEO) {
    return (overlay.videoStartTime || 0) + firstPartDuration;
  }
  if (overlay.type === OverlayType.SOUND) {
    return (overlay.startFromSound || 0) + firstPartDuration;
  }
  return 0;
};

/**
 * Creates two new overlay objects from an original overlay when splitting
 * The first half maintains the original ID and timing
 * The second half gets a new ID and adjusted timing properties
 * Preserves all other properties from the original overlay
 */
const createSplitOverlays = (
  original: Overlay,
  newId: number,
  splitFrame: number,
  firstPartDuration: number,
  secondPartDuration: number,
  secondHalfStartTime: number,
  fps: number
): [Overlay, Overlay] => {
  const msPerFrame = 1000 / fps;
  const splitTimeMs = splitFrame * msPerFrame;

  if (original.type === OverlayType.CAPTION) {
    // Calculate absolute time ranges for both splits
    const originalStartMs = original.from * msPerFrame;
    const splitOffsetMs = splitTimeMs - originalStartMs; // Time relative to overlay start

    // Split captions at word level, keeping timestamps relative to their overlay
    const firstHalfCaptions = original.captions
      .filter((caption) => caption.startMs < splitOffsetMs)
      .map((caption) => ({
        ...caption,
        endMs: Math.min(caption.endMs, splitOffsetMs),
        words: caption.words
          .filter((word) => word.startMs < splitOffsetMs)
          .map((word) => ({
            ...word,
            endMs: Math.min(word.endMs, splitOffsetMs),
          })),
      }))
      .filter((caption) => caption.words.length > 0)
      .map((caption) => ({
        ...caption,
        text: caption.words.map((w) => w.word).join(" "),
      }));

    const secondHalfCaptions = original.captions
      .filter((caption) => caption.endMs > splitOffsetMs)
      .map((caption) => ({
        ...caption,
        startMs: Math.max(0, caption.startMs - splitOffsetMs),
        endMs: caption.endMs - splitOffsetMs,
        words: caption.words
          .filter((word) => word.endMs > splitOffsetMs)
          .map((word) => ({
            ...word,
            startMs: Math.max(0, word.startMs - splitOffsetMs),
            endMs: word.endMs - splitOffsetMs,
          })),
      }))
      .filter((caption) => caption.words.length > 0)
      .map((caption) => ({
        ...caption,
        text: caption.words.map((w) => w.word).join(" "),
      }));

    // Create the split overlays with adjusted captions
    const firstHalf: CaptionOverlay = {
      ...original,
      durationInFrames: firstPartDuration,
      captions: firstHalfCaptions,
    };

    const secondHalf: CaptionOverlay = {
      ...original,
      id: newId,
      from: splitFrame,
      durationInFrames: secondPartDuration,
      captions: secondHalfCaptions,
    };

    return [firstHalf, secondHalf];
  }

  const firstHalf: Overlay = {
    ...original,
    durationInFrames: firstPartDuration,
  };

  const secondHalf: Overlay = {
    ...original,
    id: newId,
    from: splitFrame,
    durationInFrames: secondPartDuration,
    ...(original.type === OverlayType.VIDEO && {
      videoStartTime: secondHalfStartTime,
    }),
    ...(original.type === OverlayType.SOUND && {
      startFromSound: secondHalfStartTime,
    }),
  };

  return [firstHalf, secondHalf];
};
