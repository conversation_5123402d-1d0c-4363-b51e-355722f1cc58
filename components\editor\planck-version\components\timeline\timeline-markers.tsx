import React, { useState, useCallback, useEffect } from "react";
import { createPortal } from "react-dom";

interface TimeMarkersProps {
  durationInFrames: number;
  handleTimelineClick: (frame: number) => void;
  zoomScale: number;
  fps: number;
}

const TimeMarkers: React.FC<TimeMarkersProps> = ({
  durationInFrames,
  handleTimelineClick,
  zoomScale,
  fps,
}) => {
  const [hoverInfo, setHoverInfo] = useState<{
    visible: boolean;
    frame: number;
  }>({
    visible: false,
    frame: 0,
  });

  const [mouseTooltip, setMouseTooltip] = useState<{
    visible: boolean;
    x: number;
    y: number;
    frame: number;
  }>({
    visible: false,
    x: 0,
    y: 0,
    frame: 0,
  });

  const durationInSeconds = durationInFrames / fps;

  const formatHoverTime = useCallback((frame: number, fps: number): string => {
    // Convert frame to seconds and format as HH:MM:SS
    const timeInSeconds = Math.floor(frame / fps);
    const hours = Math.floor(timeInSeconds / 3600);
    const minutes = Math.floor((timeInSeconds % 3600) / 60);
    const seconds = timeInSeconds % 60;

    // Use standard HH:MM:SS format
    return (
      `${hours.toString().padStart(2, "0")}:` +
      `${minutes.toString().padStart(2, "0")}:` +
      `${seconds.toString().padStart(2, "0")}`
    );
  }, []);

  const handleMouseMove = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      const rect = e.currentTarget.getBoundingClientRect();
      const mouseX = e.clientX - rect.left;
      const percentage = Math.max(0, Math.min(1, mouseX / rect.width));

      // Ensure durationInFrames is treated as integer and calculate frame properly
      const totalFrames = Math.round(durationInFrames);
      const hoveredFrame = Math.round(percentage * totalFrames);
      const clampedFrame = Math.max(0, Math.min(totalFrames, hoveredFrame));

      setMouseTooltip({
        visible: true,
        x: e.clientX,
        y: e.clientY - 40,
        frame: clampedFrame, // Already rounded above
      });
    },
    [durationInFrames]
  );

  // Handle mouse leave
  const handleMouseLeave = useCallback(() => {
    setMouseTooltip((prev) => ({ ...prev, visible: false }));
  }, []);

  // Listen for timeline frame updates from TimelineMarker
  useEffect(() => {
    const handleFrameUpdate = (event: CustomEvent) => {
      const { frame, isDragging, realTime, formattedTime } = event.detail;

      // Show hover info during dragging or real-time updates
      if (isDragging || realTime) {
        setHoverInfo({
          visible: true,
          frame: frame,
        });
      } else {
        setHoverInfo({
          visible: false,
          frame: 0,
        });
      }
    };

    window.addEventListener(
      "timeline-frame-update",
      handleFrameUpdate as EventListener
    );

    return () => {
      window.removeEventListener(
        "timeline-frame-update",
        handleFrameUpdate as EventListener
      );
    };
  }, []);

  // Enhanced interval calculation with better spacing and zoom handling
  const getIntervals = () => {
    const visibleDuration = durationInSeconds / zoomScale;
    const totalMinutes = durationInSeconds / 60;
    const totalHours = totalMinutes / 60;

    // Calculate optimal marker density based on visible duration and zoom
    const pixelsPerSecond = 1000 * (zoomScale / 100); // Approximate pixels per second

    // Determine optimal major interval based on visible duration and zoom
    let majorInterval, minorInterval;

    if (visibleDuration <= 2) {
      // Very zoomed in (showing 2 seconds or less)
      majorInterval = 0.5; // Every 500ms
      minorInterval = 0.1; // Every 100ms
    } else if (visibleDuration <= 5) {
      // Showing 2-5 seconds
      majorInterval = 1; // Every second
      minorInterval = 0.2; // Every 200ms
    } else if (visibleDuration <= 15) {
      // Showing 5-15 seconds
      majorInterval = 2; // Every 2 seconds
      minorInterval = 0.5; // Every 500ms
    } else if (visibleDuration <= 30) {
      // Showing 15-30 seconds
      majorInterval = 5; // Every 5 seconds
      minorInterval = 1; // Every second
    } else if (visibleDuration <= 120) {
      // Showing 30s - 2 minutes
      majorInterval = 10; // Every 10 seconds
      minorInterval = 5; // Every 5 seconds
    } else if (visibleDuration <= 300) {
      // Showing 2-5 minutes
      majorInterval = 30; // Every 30 seconds
      minorInterval = 10; // Every 10 seconds
    } else if (visibleDuration <= 900) {
      // Showing 5-15 minutes
      majorInterval = 60; // Every minute
      minorInterval = 30; // Every 30 seconds
    } else if (visibleDuration <= 3600) {
      // Showing 15-60 minutes
      majorInterval = 300; // Every 5 minutes
      minorInterval = 60; // Every minute
    } else {
      // Showing more than 1 hour
      majorInterval = 600; // Every 10 minutes
      minorInterval = 300; // Every 5 minutes
    }

    // Ensure minor interval is never greater than or equal to major
    if (minorInterval >= majorInterval) {
      minorInterval = majorInterval / 2;
    }

    return {
      major: majorInterval,
      minor: minorInterval,
    };
  };

  const { major, minor } = getIntervals();

  // Simple time formatting using HH:MM:SS format consistently
  const formatTimeLabel = (seconds: number, frame: number = 0): string => {
    const totalSeconds = Math.floor(seconds);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const secs = totalSeconds % 60;

    // Always use standard HH:MM:SS format regardless of zoom level
    return (
      `${hours.toString().padStart(2, "0")}:` +
      `${minutes.toString().padStart(2, "0")}:` +
      `${secs.toString().padStart(2, "0")}`
    );
  };

  // Generate major markers (with labels)
  const majorMarkers = [];
  for (let s = 0; s <= durationInSeconds; s += major) {
    const frame = Math.round(s * fps);
    if (frame <= durationInFrames) {
      const position = (frame / durationInFrames) * 100;
      majorMarkers.push({
        seconds: s,
        frame,
        position,
        label: formatTimeLabel(s, frame),
      });
    }
  }

  // Generate minor markers (vertical lines only)
  const minorMarkers = [];
  if (minor > 0) {
    for (let s = minor; s <= durationInSeconds; s += minor) {
      // Skip if this would be a major marker
      if (Math.abs(s % major) > 0.001) {
        // Use small epsilon for floating point comparison
        const frame = Math.round(s * fps);
        if (frame <= durationInFrames) {
          const position = (frame / durationInFrames) * 100;
          minorMarkers.push({
            seconds: s,
            frame,
            position,
          });
        }
      }
    }
  }

  // FIXED: Add sub-second markers for very short videos at high zoom
  const subSecondMarkers = [];
  if (durationInSeconds <= 5 && zoomScale >= 8) {
    // Show frame-level markers for very short videos at high zoom
    const frameInterval = Math.max(1, Math.floor(fps / 10)); // Every 10th of a second
    for (
      let frame = frameInterval;
      frame < durationInFrames;
      frame += frameInterval
    ) {
      const seconds = frame / fps;
      // Skip if this would be a major or minor marker
      if (
        Math.abs(seconds % major) > 0.001 &&
        Math.abs(seconds % minor) > 0.001
      ) {
        const position = (frame / durationInFrames) * 100;
        subSecondMarkers.push({
          frame,
          position,
        });
      }
    }
  }

  // Handle timeline click
  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percentage = Math.max(0, Math.min(1, clickX / rect.width));

    // Ensure durationInFrames is treated as integer and calculate frame properly
    const totalFrames = Math.round(durationInFrames);
    const clickedFrame = Math.round(percentage * totalFrames);
    const clampedFrame = Math.max(0, Math.min(totalFrames, clickedFrame));

    // Call the provided click handler with integer frame
    handleTimelineClick(clampedFrame);
  };
  // Create mouse-following tooltip portal
  const mouseTooltipPortal = mouseTooltip.visible
    ? createPortal(
        <div
          className="fixed z-[9999] pointer-events-none"
          style={{
            left: mouseTooltip.x,
            top: mouseTooltip.y,
            transform: "translateX(-50%)",
          }}
        >
          <div className="bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs font-mono px-2 py-1 rounded shadow-lg border border-gray-700 dark:border-gray-300 whitespace-nowrap">
            {formatHoverTime(mouseTooltip.frame, fps)}
          </div>
        </div>,
        document.body
      )
    : null;

  return (
    <>
      {mouseTooltipPortal}
      <div className="relative w-full h-full">
        <div
          className="relative w-full h-full bg-gray-100 dark:bg-gray-800 cursor-pointer overflow-visible"
          onClick={handleClick}
          onMouseMove={handleMouseMove}
          onMouseLeave={handleMouseLeave}
          style={{ minHeight: "21px" }}
        >
          {/* Major markers (with labels) */}
          {majorMarkers.map((marker, index) => (
            <div
              key={`major-${index}`}
              className="absolute top-0 h-full flex flex-col items-center pointer-events-none"
              style={{
                left: `${marker.position}%`,
                transform: "translateX(-50%)",
              }}
            >
              {/* Major tick line with subtle shadow for better visibility */}
              <div className="w-[1.5px] h-full bg-gray-400 dark:bg-gray-400 shadow-sm" />

              {/* Time label with subtle background and better spacing */}
              <div className="absolute bottom-0 text-[11px] text-gray-700 dark:text-gray-200 font-mono font-medium bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm px-1.5 py-0.5 rounded-b whitespace-nowrap select-none border-t border-gray-200 dark:border-gray-600">
                {marker.label}
              </div>
            </div>
          ))}

          {/* Minor markers (vertical lines only) */}
          {minorMarkers.map((marker, index) => (
            <div
              key={`minor-${index}`}
              className="absolute top-0 w-[1px] h-1/2 bg-gray-300/80 dark:bg-gray-500/80 pointer-events-none"
              style={{
                left: `${marker.position}%`,
                transform: "translateX(-50%)",
              }}
            />
          ))}

          {/* Sub-second markers for very short videos */}
          {subSecondMarkers.map((marker, index) => (
            <div
              key={`subsecond-${index}`}
              className="absolute top-0 w-px h-1/3 bg-gray-200 dark:bg-gray-700 opacity-60 pointer-events-none"
              style={{
                left: `${marker.position}%`,
                transform: "translateX(-50%)",
              }}
            />
          ))}

          {/* Hover tooltip - shows during TimelineMarker drag */}
          {hoverInfo.visible && (
            <div
              className="absolute z-50 pointer-events-none"
              style={{
                left: `${(hoverInfo.frame / durationInFrames) * 100}%`,
                top: "-35px",
                transform: "translateX(-50%)",
              }}
            >
              <div className="bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs font-mono px-2 py-1 rounded shadow-lg border border-gray-700 dark:border-gray-300 whitespace-nowrap">
                {formatHoverTime(hoverInfo.frame, fps)}
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default TimeMarkers;
