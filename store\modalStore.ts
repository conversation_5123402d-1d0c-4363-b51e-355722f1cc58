import { create } from 'zustand';

interface ProjectManagerState {
  modalImportMedia: boolean;
  currentVideoFPS: number | null;
  setModalImportMarkers: (val: boolean) => void;
  openImportMarkersModal: () => void;
  closeImportMarkersModal: () => void;
  setCurrentVideoFPS: (fps: number | null) => void;
}

export const useProjectManager = create<ProjectManagerState>((set) => ({
  modalImportMedia: false,
  currentVideoFPS: null,
  setModalImportMarkers: (val) => set({ modalImportMedia: val }),
  openImportMarkersModal: () => set({ modalImportMedia: true }),
  closeImportMarkersModal: () => set({ modalImportMedia: false }),
  setCurrentVideoFPS: (fps) => set({ currentVideoFPS: fps }),
}));
