import React from "react";
import { Overlay } from "../../types";
import { TextLayerContent } from "../overlays/text/text-layer-content";
import { OverlayType } from "../../types";
import { CaptionLayerContent } from "../overlays/captions/caption-layer-content";
import { VideoLayerContent } from "../overlays/video/video-layer-content";
import { ImageLayerContent } from "../overlays/images/image-layer-content";
import { SoundLayerContent } from "../overlays/captions/sound-layer-content";
import { StickerLayerContent } from "../overlays/stickers/sticker-layer-content";

/**
 * Props for the LayerContent component
 * @interface LayerContentProps
 * @property {Overlay} overlay - The overlay object containing type and content information
 * @property {string | undefined} baseUrl - The base URL for media assets
 * @property {Array} mutedSegments - Array of segments that should have their audio muted
 */
interface LayerContentProps {
  overlay: Overlay;
  baseUrl?: string;
  mutedSegments: Array<{ id: number; startFrame: number; endFrame: number }>;
}

/**
 * LayerContent Component
 *
 * @component
 * @description
 * A component that renders different types of content layers in the video editor.
 * It acts as a switch component that determines which specific layer component
 * to render based on the overlay type.
 *
 * Supported overlay types:
 * - VIDEO: Renders video content with VideoLayerContent (supports audio muting)
 * - TEXT: Renders text overlays with TextLayerContent
 * - SHAPE: Renders colored shapes
 * - IMAGE: Renders images with ImageLayerContent
 * - CAPTION: Renders captions with CaptionLayerContent
 * - SOUND: Renders audio elements using Remotion's Audio component
 *
 * Each layer type maintains consistent sizing through commonStyle,
 * with specific customizations applied as needed.
 *
 * Audio muting functionality:
 * When segments have generated AI audio, VIDEO overlays will automatically mute their audio
 * during those segments' time ranges to allow AI-generated audio to play clearly.
 */
export const LayerContent: React.FC<LayerContentProps> = ({
  overlay,
  baseUrl,
  mutedSegments, // 🔥 FIXED: Use mutedSegments parameter
}) => {

  /**
   * Common styling applied to all layer types
   * Ensures consistent dimensions across different content types
   */
  const commonStyle: React.CSSProperties = {
    width: "100%",
    height: "100%",
  };

  switch (overlay.type) {
    case OverlayType.VIDEO:
      return (
        <div style={{ ...commonStyle }}>
          <VideoLayerContent
            overlay={overlay}
            baseUrl={baseUrl}
            // 🔥 FIXED: Pass mutedSegments instead of individual segment props
            mutedSegments={mutedSegments}
          />
        </div>
      );

    case OverlayType.TEXT:
      return (
        <div style={{ ...commonStyle }}>
          <TextLayerContent overlay={overlay} />
        </div>
      );

    case OverlayType.IMAGE:
      return (
        <div style={{ ...commonStyle }}>
          <ImageLayerContent overlay={overlay} baseUrl={baseUrl} />
        </div>
      );

    case OverlayType.CAPTION:
      return (
        <div
          style={{
            ...commonStyle,
            position: "relative",
            overflow: "hidden",
            display: "flex",
          }}
        >
          <CaptionLayerContent overlay={overlay} />
        </div>
      );

    case OverlayType.STICKER:
      return (
        <div style={{ ...commonStyle }}>
          <StickerLayerContent overlay={overlay} isSelected={false} />
        </div>
      );

    case OverlayType.SOUND:
      return (
        <SoundLayerContent
          overlay={overlay}
          baseUrl={baseUrl}
          // Note: Sound overlays don't need muting props since they're separate audio tracks
        />
      );

    default:
      return null;
  }
};
