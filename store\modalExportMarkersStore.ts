import { create } from 'zustand';

interface ExportMarkersModalState {
  modalExportMarkers: boolean;
  setModalExportMarkers: (val: boolean) => void;
  openExportMarkersModal: () => void;
  closeExportMarkersModal: () => void;
}

export const useExportMarkersModal = create<ExportMarkersModalState>((set) => ({
  modalExportMarkers: false,
  setModalExportMarkers: (val) => set({ modalExportMarkers: val }),
  openExportMarkersModal: () => set({ modalExportMarkers: true }),
  closeExportMarkersModal: () => set({ modalExportMarkers: false }),
}));

