const { app, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>u, ipc<PERSON>ain, dialog } = require("electron");
const { spawn, exec } = require("child_process");
const http = require("http");

const fs = require("fs");
const path = require("path");

function makeHttpRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: options.method || "GET",
      headers: options.headers || {},
      // Remove timeout for analysis requests - allow unlimited time for Python processing
      timeout: options.noTimeout ? 0 : options.timeout || 10000,
    };

    const req = http.request(requestOptions, (res) => {
      let data = "";
      res.on("data", (chunk) => {
        data += chunk;
        // Stream real-time output from Python server
        if (options.onData && typeof options.onData === "function") {
          options.onData(chunk.toString());
        }
      });
      res.on("end", () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data)),
          text: () => Promise.resolve(data),
        });
      });
    });

    req.on("error", (err) => {
      reject(err);
    });

    // Only set timeout handling if timeout is specified and > 0
    if (requestOptions.timeout > 0) {
      req.on("timeout", () => {
        req.destroy();
        reject(new Error("Request timeout"));
      });
    }

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}
let win;
let splashWindow = null;
let pythonServerProcess = null;
let serverStartupPromise = null;
const isDev = !app.isPackaged;

let currentProjectPath = null;
let menuItemSave;
let menuItemSaveAs;
let mainWindow = null;
let currentProjectTitle = null;

// PYTHON SERVER CONFIGURATION
const PYTHON_SERVER_PORT = 8000;
const PYTHON_SERVER_URL = `http://127.0.0.1:${PYTHON_SERVER_PORT}`;

// Function to update window title
function updateWindowTitle(projectTitle = null) {
  if (mainWindow && !mainWindow.isDestroyed()) {
    const baseTitle = "PLANCK";
    if (projectTitle && projectTitle.trim() !== "") {
      const title = `${baseTitle} - ${projectTitle.trim()}`;
      mainWindow.setTitle(title);
      currentProjectTitle = projectTitle.trim();
      console.log(`✅ Window title updated to: ${title}`);
    } else {
      mainWindow.setTitle(baseTitle);
      currentProjectTitle = null;
      console.log(`✅ Window title reset to: ${baseTitle}`);
    }
  }
}

// Function to enable/disable save menu items
function updateSaveMenuItems(enabled) {
  if (mainWindow && !mainWindow.isDestroyed()) {
    const menu = Menu.getApplicationMenu();
    if (menu) {
      const addCutMarkerItem = menu.getMenuItemById("addCutMarker");
      const addManualCutMarkerItem = menu.getMenuItemById("AddManualCutMarker");
      const selectCutMarkersItem = menu.getMenuItemById("selectCutMarkers");
      if (addCutMarkerItem) {
        addCutMarkerItem.enabled = enabled;
      }
      if (addManualCutMarkerItem) {
        addManualCutMarkerItem.enabled = enabled;
      }
      if (selectCutMarkersItem) {
        selectCutMarkersItem.enabled = enabled;
      }
      const saveItem = menu.getMenuItemById("saveMenuItem");
      const saveAsItem = menu.getMenuItemById("saveAsMenu");
      const importMediaItem = menu.getMenuItemById("importMedia");
      const importItem = menu.getMenuItemById("import");
      const exportMarkersItem = menu.getMenuItemById("exportMarkers");
      const exportItem = menu.getMenuItemById("export");
      const newProjectItem = menu.getMenuItemById("newProject");
      if (saveItem) {
        saveItem.enabled = enabled;
      }
      if (saveAsItem) {
        saveAsItem.enabled = enabled;
      }
      if (importMediaItem) {
        importMediaItem.enabled = enabled;
      }
      if (importItem) {
        importItem.enabled = enabled;
      }
      if (exportMarkersItem) {
        exportMarkersItem.enabled = enabled;
      }
      if (exportItem) {
        exportItem.enabled = enabled;
      }
      if (newProjectItem) {
        newProjectItem.enabled = enabled;
      }
      const weatherModifierItem = menu.getMenuItemById("weatherModifier");
      const environmentModifierItem = menu.getMenuItemById(
        "environmentModifier"
      );
      const groundTextureModifierItem = menu.getMenuItemById(
        "groundTextureModifier"
      );
      const groundMaterialsModifierItem = menu.getMenuItemById(
        "groundMaterialsModifier"
      );
      const footwearModifierItem = menu.getMenuItemById("footwearModifier");
      if (weatherModifierItem) {
        weatherModifierItem.enabled = enabled;
      }
      if (environmentModifierItem) {
        environmentModifierItem.enabled = enabled;
      }
      if (groundTextureModifierItem) {
        groundTextureModifierItem.enabled = enabled;
      }
      if (groundMaterialsModifierItem) {
        groundMaterialsModifierItem.enabled = enabled;
      }
      if (footwearModifierItem) {
        footwearModifierItem.enabled = enabled;
      }
    }
  }
}
function createSplashWindow() {
  splashWindow = new BrowserWindow({
    width: 1024,
    height: 768,
    frame: false,
    transparent: true,
    alwaysOnTop: false,
    resizable: false,
    show: true,
    backgroundColor: "#00000000",
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
    },
    roundedCorners: true,
    titleBarStyle: "hidden",
    titleBarOverlay: false,
  });

  // Set the window to be frameless but with rounded corners
  splashWindow.setBackgroundColor("#00000000");
  splashWindow.setHasShadow(true);
  splashWindow.loadFile(path.join(__dirname, "splash.html"));

  // Center the window
  splashWindow.center();
  return splashWindow;
}

async function createWindow() {
  // Show splash screen
  const splash = createSplashWindow();

  // Create the main window but keep it hidden for now
  win = new BrowserWindow({
    width: 1280,
    height: 800,
    show: false, // Keep hidden initially - CHANGED from true
    backgroundColor: "#000000", // Set background to black to match splash
    title: "PLANCK", // Default title
    webPreferences: {
      preload: path.join(__dirname, "preload.js"),
      nodeIntegration: false,
      contextIsolation: true,
    },
    maximizable: true,
    minWidth: 1280,
    minHeight: 800,
    frame: true,
    transparent: false,
  });

  // Store reference to main window
  mainWindow = win;

  // DON'T load the URL here - wait for Python server
  // await win.loadURL('http://localhost:3000'); // REMOVE THIS LINE

  console.log("Preload path:", path.join(__dirname, "preload.js"));

  const menuTemplate = [
    {
      label: app.name,
      submenu: [{ role: "about" }, { type: "separator" }, { role: "quit" }],
    },
    {
      label: "File",
      submenu: [
        {
          label: "New Project",
          id: "newProject",
          enabled: false,
          accelerator: "CmdOrCtrl+N",
          click: async () => {
            const focusedWindow = BrowserWindow.getFocusedWindow();
            if (!focusedWindow) return;

            focusedWindow.webContents.send("menu-new-project");
          },
        },
        {
          label: "Open Project",
          accelerator: "CmdOrCtrl+O",
          click: async () => {
            const focusedWindow = BrowserWindow.getFocusedWindow();
            if (!focusedWindow) return;

            focusedWindow.webContents.send("menu-open-project");
          },
        },
        {
          label: "Save Project",
          accelerator: "CmdOrCtrl+S",
          id: "saveMenuItem",
          enabled: false,
          click: async () => {
            const focusedWindow = BrowserWindow.getFocusedWindow();
            if (!focusedWindow) return;

            focusedWindow.webContents.send("menu-save");
          },
        },
        {
          label: "Save As",
          accelerator: "CmdOrCtrl+Shift+S",
          id: "saveAsMenu",
          enabled: false,
          click: async () => {
            const focusedWindow = BrowserWindow.getFocusedWindow();
            if (!focusedWindow) return;

            focusedWindow.webContents.send("menu-save-as");
          },
        },
        { type: "separator" },
        {
          label: "Import Media",
          id: "importMedia",
          enabled: false,
          click: async () => {
            const focusedWindow = BrowserWindow.getFocusedWindow();
            if (!focusedWindow) return;

            focusedWindow.webContents.send("menu-import-media");
          },
        },
        {
          label: "Import XML, EDL, FCPX XML",
          id: "import",
          enabled: false,
          click: async () => {
            const focusedWindow = BrowserWindow.getFocusedWindow();
            if (!focusedWindow) return;

            focusedWindow.webContents.send("menu-import");
          },
        },
        {
          label: "Export Markers XML, EDL, FCPX XML",
          id: "exportMarkers",
          enabled: false,
          click: async () => {
            const focusedWindow = BrowserWindow.getFocusedWindow();
            if (!focusedWindow) return;

            focusedWindow.webContents.send("menu-export-markers");
          },
        },
        {
          label: "Export AAF, WAV",
          id: "export",
          enabled: false,
          click: async () => {
            const focusedWindow = BrowserWindow.getFocusedWindow();
            if (!focusedWindow) return;

            focusedWindow.webContents.send("menu-export");
          },
        },
      ],
    },
    {
      label: "Edit",
      submenu: [
        {
          label: "Add Cut Marker",
          enabled: false,
          id: "addCutMarker",
          click: async () => {
            mainWindow.webContents.send("menu-add-cut-marker");
          },
        },
        {
          label: "Add Manual Cut Marker",
          enabled: false,
          id: "AddManualCutMarker",
          click: async () => {
            mainWindow.webContents.send("menu-add-manual-cut-marker");
          },
        },
        {
          label: "Select Cut Markers",
          enabled: false,
          id: "selectCutMarkers",
          click: async () => {
            mainWindow.webContents.send("menu-select-cut-marker");
          },
        },
      ],
    },
    {
      label: "Options",
      submenu: [
        {
          label: "Weather Modifier",
          id: "weatherModifier",
          enabled: true,
          click: async () => {
            const focusedWindow = BrowserWindow.getFocusedWindow();
            if (!focusedWindow) return;

            focusedWindow.webContents.send("menu-analytics", {
              field: "weather",
            });
          },
        },
        {
          label: "Environment Modifier",
          id: "environmentModifier",
          enabled: true,
          click: async () => {
            const focusedWindow = BrowserWindow.getFocusedWindow();
            if (!focusedWindow) return;

            focusedWindow.webContents.send("menu-analytics", {
              field: "environment",
            });
          },
        },
        {
          label: "Ground Texture Modifier",
          id: "groundTextureModifier",
          enabled: true,
          click: async () => {
            const focusedWindow = BrowserWindow.getFocusedWindow();
            if (!focusedWindow) return;

            focusedWindow.webContents.send("menu-analytics", {
              field: "groundTexture",
            });
          },
        },
        {
          label: "Ground Materials Modifier",
          id: "groundMaterialsModifier",
          enabled: true,
          click: async () => {
            const focusedWindow = BrowserWindow.getFocusedWindow();
            if (!focusedWindow) return;

            focusedWindow.webContents.send("menu-analytics", {
              field: "groundMaterial",
            });
          },
        },
        {
          label: "Footwear Modifier",
          id: "footwearModifier",
          enabled: true,
          click: async () => {
            const focusedWindow = BrowserWindow.getFocusedWindow();
            if (!focusedWindow) return;

            focusedWindow.webContents.send("menu-analytics", {
              field: "footwear",
            });
          },
        },
      ],
    },
    {
      label: "Help",
      submenu: [
        {
          label: "FAQ",
        },
        {
          label: "Manual",
        },
        { role: "toggledevtools" },
      ],
    },
  ];

  const menu = Menu.buildFromTemplate(menuTemplate);
  Menu.setApplicationMenu(menu);
  // Menu.setApplicationMenu(null);

  const isDev = !app.isPackaged;
  const startUrl = isDev
    ? "http://localhost:3000"
    : `file://${path.join(__dirname, "/out/index.html")}`;

  return { win, startUrl, splash }; // Return splash reference too
}

let isQuitting = false;

app.on("before-quit", (event) => {
  if (pythonServerProcess && !isQuitting) {
    console.log("🛑 App quitting, stopping Python server...");
    event.preventDefault(); // Prevent app from quitting immediately
    isQuitting = true;

    const process = pythonServerProcess;

    // Try graceful shutdown first
    try {
      process.kill("SIGTERM");
    } catch (error) {
      console.log("⚠️ Error sending SIGTERM:", error.message);
    }

    // Force quit after 3 seconds even if Python server doesn't respond
    const forceQuitTimer = setTimeout(() => {
      console.log("🔥 Force killing Python server and quitting");
      try {
        if (process.platform === "win32") {
          require("child_process").exec(`taskkill /pid ${process.pid} /T /F`);
        } else {
          process.kill("SIGKILL");
        }
      } catch (error) {
        console.log("⚠️ Error force killing:", error.message);
      }
      pythonServerProcess = null;
      serverStartupPromise = null;
      // Use setImmediate to allow cleanup, then quit
      setImmediate(() => {
        app.exit(0);
      });
    }, 3000);

    // Wait for Python server to actually exit, then allow quit
    process.on("exit", () => {
      console.log("✅ Python server stopped, continuing app quit");
      clearTimeout(forceQuitTimer);
      pythonServerProcess = null;
      serverStartupPromise = null;
      // Use setImmediate to allow cleanup, then quit
      setImmediate(() => {
        app.exit(0);
      });
    });
  }

  // Ensure splash window is closed when app quits
  if (splashWindow && !splashWindow.isDestroyed()) {
    splashWindow.close();
  }
});

// app.whenReady().then(createWindow);
app.whenReady().then(async () => {
  const { win, startUrl, splash } = await createWindow();

  console.log("🚀 Starting Python server...");

  // Wait for Python server to start with retries
  let serverResult;
  const maxRetries = 3;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    console.log(
      `🔄 Attempt ${attempt}/${maxRetries} to start Python server...`
    );

    serverResult = await startPythonServer();

    if (serverResult.success) {
      console.log("✅ Python server ready, loading app...");
      break;
    } else {
      console.warn(`⚠️ Attempt ${attempt} failed:`, serverResult.error);

      if (attempt < maxRetries) {
        console.log("⏳ Waiting 2s before retry...");
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }
    }
  }

  if (serverResult.success) {
    win.loadURL(startUrl);
    splash.close();
    win.maximize();
    win.show();
  } else {
    // Show error dialog and handle failure
    const response = await dialog.showMessageBox(win, {
      type: "error",
      title: "Server Error",
      message: "Failed to start Python server",
      buttons: ["Retry", "Exit"],
      defaultId: 0,
    });

    if (response.response === 0) {
      splash.close();
      app.relaunch();
      app.exit();
    } else {
      splash.close();
      app.quit();
    }
  }
});

app.on("window-all-closed", () => {
  if (process.platform !== "darwin") app.quit();
});

function logToFile(message) {
  const logPath = path.join(__dirname, "plank-debug.log");
  const timestamp = new Date().toISOString();
  fs.appendFileSync(logPath, `[${timestamp}] ${message}\n`);
}

async function checkPythonInstallation() {
  return new Promise((resolve) => {
    exec("python --version", (error, stdout, stderr) => {
      if (error) {
        // Try python3
        exec("python3 --version", (error3, stdout3, stderr3) => {
          if (error3) {
            resolve({ installed: false, command: null });
          } else {
            resolve({
              installed: true,
              command: "python3",
              version: stdout3.trim(),
            });
          }
        });
      } else {
        resolve({ installed: true, command: "python", version: stdout.trim() });
      }
    });
  });
}

function getPythonEnvironmentPath() {
  if (isDev) {
    // Dev: use .venv in project root
    return path.join(__dirname, "python-scripts", ".venv");
  } else {
    // Production: create .venv next to the Python script
    console.log(
      `getPythonEnvironmentPath: process.resourcesPath = ${process.resourcesPath}`
    );
    logToFile(
      `getPythonEnvironmentPath: process.resourcesPath = ${process.resourcesPath}`
    );
    return path.join(process.resourcesPath, "python-scripts", ".venv");
  }
}

async function installPythonDependencies() {
  console.log("🐍 === INSTALLING PYTHON DEPENDENCIES ===");
  logToFile("🐍 === INSTALLING PYTHON DEPENDENCIES ===");

  const VENV_DIR = getPythonEnvironmentPath();
  const requirementsPath = getRequirementsPath();
  const isWindows = process.platform === "win32";

  try {
    // STEP 1: Check Python installation
    console.log("📋 Step 1: Checking Python installation...");
    const pythonCheck = await checkPythonInstallation();

    if (!pythonCheck.installed) {
      console.error("❌ CRITICAL: Python is not installed or not in PATH");
      logToFile("❌ CRITICAL: Python is not installed or not in PATH");
      app.quit();
      throw new Error("Python is not installed or not in PATH");
    }
    console.log(
      `✅ Found Python: ${pythonCheck.command} (${pythonCheck.version})`
    );
    logToFile(`Found Python: ${pythonCheck.command} (${pythonCheck.version})`);

    // STEP 1.5: Check MMAudio folder and clone if missing
    console.log("📋 Step 1.5: Checking MMAudio folder...");
    const scriptDir = isDev
      ? path.join(__dirname, "python-scripts")
      : path.join(process.resourcesPath, "python-scripts");
    const mmaudioPath = path.join(scriptDir, "mmaudio");

    if (!fs.existsSync(mmaudioPath)) {
      console.log("📥 MMAudio folder not found, cloning from GitHub...");
      logToFile("📥 MMAudio folder not found, cloning from GitHub...");

      await new Promise((resolve, reject) => {
        const { exec } = require("child_process");
        const cloneCommand = `git clone https://github.com/hkchengrex/MMAudio.git "${mmaudioPath}"`;

        exec(
          cloneCommand,
          { cwd: scriptDir, timeout: 300000 },
          (error, stdout, stderr) => {
            if (error) {
              console.error(
                "❌ CRITICAL: Failed to clone MMAudio:",
                error.message
              );
              logToFile(
                `❌ CRITICAL: Failed to clone MMAudio: ${error.message}`
              );
              app.quit();
              reject(new Error(`Failed to clone MMAudio: ${error.message}`));
            } else {
              console.log("✅ MMAudio cloned successfully");
              logToFile("✅ MMAudio cloned successfully");
              resolve();
            }
          }
        );
      });
    } else {
      console.log("✅ MMAudio folder already exists");
      logToFile("✅ MMAudio folder already exists");
    }

    // STEP 2: Create .venv if not exists
    console.log("📋 Step 2: Creating virtual environment...");
    if (!fs.existsSync(VENV_DIR)) {
      // Make sure parent directory exists
      fs.mkdirSync(path.dirname(VENV_DIR), { recursive: true });

      await new Promise((resolve, reject) => {
        console.log("🔨 Creating .venv...");
        console.log(
          `  Executing: ${pythonCheck.command} -m venv "${VENV_DIR}"`
        );
        exec(
          `${pythonCheck.command} -m venv "${VENV_DIR}"`,
          {
            timeout: 60000,
            cwd: path.dirname(VENV_DIR),
          },
          (error, stdout, stderr) => {
            if (error) {
              reject(new Error(`Failed to create .venv: ${error.message}`));
            } else {
              console.log("✅ .venv created successfully");
              logToFile("✅ .venv created successfully");
              resolve();
            }
          }
        );
      });
    } else {
      console.log("✅ .venv already exists");
      logToFile("✅ .venv already exists");
    }

    // STEP 3: Install dependencies
    console.log("📋 Step 3: Installing dependencies...");
    const venvPipPath = isWindows
      ? fs.existsSync(path.join(VENV_DIR, "Scripts", "pip.exe"))
        ? path.join(VENV_DIR, "Scripts", "pip.exe")
        : path.join(VENV_DIR, "bin", "pip.exe")
      : path.join(VENV_DIR, "bin", "pip");

    if (!fs.existsSync(venvPipPath)) {
      console.error(
        `❌ CRITICAL: Virtual environment pip not found at: ${venvPipPath}`
      );
      logToFile(
        `❌ CRITICAL: Virtual environment pip not found at: ${venvPipPath}`
      );
      app.quit();
      throw new Error(`Virtual environment pip not found at: ${venvPipPath}`);
    }

    // Check if requirements.txt exists
    if (!fs.existsSync(requirementsPath)) {
      console.log(`⚠️ Requirements.txt not found at: ${requirementsPath}`);
      console.log("📦 Installing basic packages only...");

      await new Promise((resolve, reject) => {
        const installCommand = `"${venvPipPath}" install fastapi uvicorn[standard] pydantic`;
        exec(installCommand, { timeout: 300000 }, (error, stdout, stderr) => {
          if (error) {
            reject(
              new Error(`Failed to install basic packages: ${error.message}`)
            );
          } else {
            console.log("✅ Basic packages installed successfully");
            resolve();
          }
        });
      });
    } else {
      console.log(`✅ Found requirements.txt: ${requirementsPath}`);

      await new Promise((resolve, reject) => {
        const installCommand = `"${venvPipPath}" install -r "${requirementsPath}"`;
        console.log(`📦 Installing from requirements: ${installCommand}`);

        const childProcess = exec(
          installCommand,
          { timeout: 300000 },
          (error, stdout, stderr) => {
            if (error) {
              reject(
                new Error(
                  `Failed to install from requirements: ${error.message}`
                )
              );
            } else {
              console.log(
                "✅ Dependencies from requirements.txt installed successfully"
              );
              resolve();
            }
          }
        );

        // Print output in real-time
        childProcess.stdout.on("data", (data) => {
          process.stdout.write(data);
        });

        childProcess.stderr.on("data", (data) => {
          process.stderr.write(data);
        });
      });
    }

    console.log("🎉 === PYTHON DEPENDENCIES INSTALLATION COMPLETE ===");
    logToFile("=== PYTHON DEPENDENCIES INSTALLATION COMPLETE ===");

    return {
      success: true,
      venvDir: VENV_DIR,
      pythonCommand: pythonCheck.command,
      pythonVersion: pythonCheck.version,
      requirementsFound: fs.existsSync(requirementsPath),
    };
  } catch (error) {
    console.error("❌ === PYTHON DEPENDENCIES INSTALLATION FAILED ===");
    console.error("❌ Error:", error.message);
    logToFile(
      `=== PYTHON DEPENDENCIES INSTALLATION FAILED ===\nError: ${error.message}`
    );

    return {
      success: false,
      error: error.message,
      suggestion: "Check Python installation and permissions",
    };
  }
}

function getPythonScriptPath() {
  if (isDev) {
    // Dev: use the original Python script
    return path.join(__dirname, "python-scripts", "sound_post_server.py");
  } else {
    // Production: use the compiled executable
    const getServerExecutable = () => {
      if (process.platform === "win32") {
        return "sound-post-server.exe";
      }
      return "sound-post-server"; // For macOS and Linux
    };

    return path.join(
      process.resourcesPath,
      "python-scripts",
      getServerExecutable()
    );
  }
}

async function startPythonServer() {
  if (serverStartupPromise) {
    return serverStartupPromise;
  }

  serverStartupPromise = new Promise(async (resolve) => {
    try {
      console.log("🔍 Checking if Python server is already running...");

      // Check if server is already running
      try {
        const response = await makeHttpRequest(`${PYTHON_SERVER_URL}/health`);
        if (response.ok) {
          console.log("✅ Python server already running");
          resolve({ success: true, alreadyRunning: true });
          return;
        }
      } catch (e) {
        console.log("🔍 Server not running, starting new instance...");
      }

      // Check Python installation
      const pythonCheck = await checkPythonInstallation();
      if (!pythonCheck.installed) {
        resolve({
          success: false,
          error: "Python is not installed or not in PATH",
        });
        return;
      }

      // Install dependencies
      const depInstall = await installPythonDependencies();
      if (!depInstall.success) {
        resolve({
          success: false,
          error: "Failed to install Python dependencies",
          details: depInstall.error,
        });
        return;
      }

      // Find the Python script
      const serverPath = getPythonScriptPath();
      if (!fs.existsSync(serverPath)) {
        resolve({
          success: false,
          error: `Python script not found at: ${serverPath}`,
        });
        return;
      }

      console.log(`🚀 Starting Python server from: ${serverPath}`);
      console.log(`📁 Current working directory: ${process.cwd()}`);

      if (isDev) {
        // Development: Use Python interpreter with the script
        const venvDir = getPythonEnvironmentPath();
        const isWindows = process.platform === "win32";
        const venvPythonPath = isWindows
          ? path.join(venvDir, "Scripts", "python.exe")
          : path.join(venvDir, "bin", "python");

        const pythonCommand = fs.existsSync(venvPythonPath)
          ? venvPythonPath
          : pythonCheck.command;

        console.log(`🚀 Starting Python server...`);
        console.log(`📝 Command: ${pythonCommand}`);
        console.log(`📝 Args: ${JSON.stringify([serverPath])}`);

        pythonServerProcess = spawn(pythonCommand, [serverPath], {
          stdio: ["pipe", "pipe", "pipe"],
          cwd: path.dirname(serverPath),
          env: { ...process.env, PYTHONUNBUFFERED: "1" },
        });
      } else {
        // Production: Run the executable directly
        console.log(`🚀 Starting server executable...`);
        console.log(`📝 Command: ${serverPath}`);

        pythonServerProcess = spawn(serverPath, [], {
          stdio: ["pipe", "pipe", "pipe"],
          cwd: path.dirname(serverPath),
          env: { ...process.env, PYTHONUNBUFFERED: "1" },
        });
      }

      let serverOutput = "";
      let serverError = "";
      let serverReady = false;

      pythonServerProcess.stdout.on("data", (data) => {
        const output = data.toString();
        serverOutput += output;
        console.log(`🐍 Server stdout: ${output.trim()}`);

        // Check if server is ready to receive requests
        if (output.includes("Uvicorn running on") && !serverReady) {
          console.log("🚀 Python server is ready, starting health checks...");
          serverReady = true;
          // Start health checks after server reports it's running
          setTimeout(checkServer, 2000);
        }
      });

      pythonServerProcess.stderr.on("data", (data) => {
        const error = data.toString();
        serverError += error;
        console.error(`🐍 Server stderr: ${error.trim()}`);

        // Check if server is ready to receive requests (Uvicorn outputs to stderr)
        if (error.includes("Uvicorn running on") && !serverReady) {
          console.log("🚀 Python server is ready, starting health checks...");
          serverReady = true;
          // Start health checks after server reports it's running
          setTimeout(checkServer, 2000);
        }
      });

      pythonServerProcess.on("close", (code) => {
        console.log(`🐍 Python server process exited with code ${code}`);
        if (code !== 0) {
          console.error(`🐍 Server output: ${serverOutput}`);
          console.error(`🐍 Server error: ${serverError}`);
        }
        pythonServerProcess = null;
        serverStartupPromise = null;
      });

      pythonServerProcess.on("error", (error) => {
        console.error("🐍 Failed to start Python server:", error);
        resolve({
          success: false,
          error: `Failed to start Python server: ${error.message}`,
        });
      });

      // Wait for server to be ready
      let attempts = 0;
      const maxAttempts = 120; // 2 minutes for ML model loading

      const checkServer = async () => {
        attempts++;
        console.log(`🔍 Health check attempt ${attempts}/${maxAttempts}`);
        console.log(`🔍 Checking URL: ${PYTHON_SERVER_URL}/health`);

        try {
          const response = await makeHttpRequest(`${PYTHON_SERVER_URL}/health`);
          console.log(`🔍 Health check response status: ${response.status}`);

          if (response.ok) {
            console.log("✅ Python AI server is ready!");
            resolve({
              success: true,
              port: PYTHON_SERVER_PORT,
              url: PYTHON_SERVER_URL,
            });
          } else {
            console.log("🔍 Health check error response:", response);
            throw new Error(`Server responded with status ${response.status}`);
          }
        } catch (error) {
          console.log(`🔍 Health check error: ${error.message}`);

          if (attempts >= maxAttempts) {
            console.log(
              `🔍 Max attempts reached. Server output: ${serverOutput}`
            );
            console.log(`🔍 Server error: ${serverError}`);
            console.error(
              "❌ CRITICAL: Python server failed to start within 2 minutes"
            );
            logToFile(
              "❌ CRITICAL: Python server failed to start within 2 minutes"
            );
            app.quit();
            resolve({
              success: false,
              error: "Python server failed to start within 2 minutes",
              serverOutput,
              serverError,
            });
          } else {
            setTimeout(checkServer, 1000);
          }
        }
      };

      // Health checks will start when server outputs "Uvicorn running on"
    } catch (error) {
      console.error("❌ CRITICAL Error in startPythonServer:", error);
      logToFile(`❌ CRITICAL Error in startPythonServer: ${error.message}`);
      app.quit();
      resolve({
        success: false,
        error: error.message,
      });
    }
  });

  return serverStartupPromise;
}

function getRequirementsPath() {
  if (isDev) {
    // Dev: look in python-scripts folder
    return path.join(__dirname, "python-scripts", "requirements.txt");
  } else {
    // Production: look in extraResources
    return path.join(
      process.resourcesPath,
      "python-scripts",
      "requirements.txt"
    );
  }
}

ipcMain.handle("clear-project", async () => {
  console.log("Check Clear Project");
});

ipcMain.handle("read-directory", async (event, dirPath, options = {}) => {
  try {
    if (options.recursive) {
      const results = [];
      const readDirRecursive = (currentPath) => {
        const items = fs.readdirSync(currentPath, { withFileTypes: true });
        for (const item of items) {
          const fullPath = path.join(currentPath, item.name);
          results.push(fullPath);
          if (item.isDirectory()) {
            readDirRecursive(fullPath);
          }
        }
      };
      readDirRecursive(dirPath);
      return results;
    } else {
      return fs.readdirSync(dirPath);
    }
  } catch (error) {
    console.error(`Error reading directory ${dirPath}:`, error);
    return [];
  }
});

ipcMain.on("minimize-window", () => win.minimize());
ipcMain.on("maximize-window", () =>
  win.isMaximized() ? win.unmaximize() : win.maximize()
);
ipcMain.on("close-window", () => win.close());

let lastSavedMetadata = null;
global.lastSavedMetadata = null;

// ipcMain.handle(
//   "save-video",
//   async (event, buffer, metadata, aiVideoAnalytics) => {
//     try {
//       console.log('CHECK LAST SAVED METADATA 1', global.lastSavedMetadata)
//       console.log('CHECK LAST SAVED METADATA 2', lastSavedMetadata)
//     } catch (error) {
//       console.log('CHECK ERROR', error)
//     }
//   }
// );

ipcMain.handle(
  "save-video",
  async (event, buffer, metadata, aiVideoAnalytics) => {
    try {
      let filePath = null;
      let folderPath = null;
      let metaPath = null;
      let videoName = null;
      let planckName = null;

      const isMetadataOnly = !buffer;
      const win = BrowserWindow.fromWebContents(event.sender);

      // 🔥 CRITICAL FIX: For metadata-only saves, NEVER show dialog
      if (isMetadataOnly) {
        // Always try to get existing metadata first
        if (lastSavedMetadata?.savedPath) {
          // Use existing project paths
          folderPath =
            lastSavedMetadata.saveDir ||
            path.dirname(lastSavedMetadata.savedPath);
          filePath = path.join(folderPath, lastSavedMetadata.video);
          metaPath = path.join(folderPath, lastSavedMetadata.planckName);
          videoName = lastSavedMetadata.video;
          planckName = lastSavedMetadata.planckName;

          const projectName = path.parse(planckName).name;
          updateWindowTitle(projectName);

          console.log(
            "💾 Metadata-only save: USING EXISTING PROJECT",
            folderPath
          );
        } else if (global.lastSavedMetadata?.savedPath) {
          // Try global metadata as fallback
          folderPath =
            global.lastSavedMetadata.saveDir ||
            path.dirname(global.lastSavedMetadata.savedPath);
          filePath = path.join(folderPath, global.lastSavedMetadata.video);
          metaPath = path.join(folderPath, global.lastSavedMetadata.planckName);
          videoName = global.lastSavedMetadata.video;
          planckName = global.lastSavedMetadata.planckName;

          // Sync local with global
          lastSavedMetadata = global.lastSavedMetadata;

          const projectName = path.parse(planckName).name;
          updateWindowTitle(projectName);

          console.log(
            "💾 Metadata-only save: USING GLOBAL METADATA",
            folderPath
          );
        } else {
          // No existing project found - return error instead of showing dialog
          console.error(
            "❌ Metadata-only save failed: No existing project found"
          );
          return {
            success: false,
            message:
              "No existing project found. Please save the project with video first.",
          };
        }
      } else {
        // 🔥 SAVE PROJECT (video + metadata)

        if (lastSavedMetadata?.savedPath) {
          // 🚀 Overwrite existing project
          folderPath =
            lastSavedMetadata.saveDir ||
            path.dirname(lastSavedMetadata.savedPath);
          filePath = path.join(folderPath, lastSavedMetadata.video);
          metaPath = path.join(folderPath, lastSavedMetadata.planckName);

          videoName = lastSavedMetadata.video;
          planckName = lastSavedMetadata.planckName;

          const projectName = path.parse(planckName).name;
          updateWindowTitle(projectName);

          console.log("💾 Save mode: OVERWRITE EXISTING PROJECT", folderPath);
        } else {
          // 🚀 First time save → show dialog
          const { canceled, filePath: chosenPath } =
            await dialog.showSaveDialog(win, {
              title: "Save Video",
              defaultPath: "My Project",
              filters: [
                { name: "Planck Files", extensions: ["*"] },
                { name: "All Files", extensions: ["*"] },
              ],
            });

          if (canceled || !chosenPath) {
            return { success: false, message: "Save cancelled by user" };
          }

          const parsed = path.parse(chosenPath);
          folderPath = path.join(path.dirname(chosenPath), parsed.name);

          if (!fs.existsSync(folderPath)) {
            fs.mkdirSync(folderPath, { recursive: true });
          }

          videoName = parsed.name + ".mp4";
          planckName = parsed.name + ".planck";

          filePath = path.join(folderPath, videoName);
          metaPath = path.join(folderPath, planckName);

          updateWindowTitle(parsed.name);
          console.log("💾 Save mode: NEW PROJECT", folderPath);
        }
      }

      // ✅ Save video (only if not metadata-only)
      if (!isMetadataOnly) {
        fs.writeFileSync(filePath, buffer);
        console.log("✅ Video file saved:", filePath);
      } else {
        console.log("⏭️ Skipping video save (metadata-only)");
      }

      // ✅ Ambil analytics lama (kalau ada)
      const existingAnalytics =
        lastSavedMetadata?.segments?.aiVideoAnalytics || {};

      // ✅ Analytics yang dikirim dari frontend
      const providedAnalytics = metadata?.segments?.aiVideoAnalytics || {};

      // ✅ Gabungkan (frontend > existing)
      let updatedAnalytics = {
        ...existingAnalytics,
        ...providedAnalytics,
      };

      // Simpan destinationPath
      updatedAnalytics.destinationPath = folderPath;

      // ✅ Kalau ada metadata segmentId → buat segment data
      if (metadata && metadata.segmentId !== undefined) {
        const segmentKey = `segment${metadata.segmentId}`;
        const segmentData = {
          segmentId: metadata.segmentId,
          segmentName:
            metadata.segmentName || `Custom Segment ${metadata.segmentId}`,
          startTime: metadata.startTime || 0,
          endTime: metadata.endTime || 0,

          ...(metadata.audioFile && { audioFile: metadata.audioFile }),
          ...(metadata.characterSize && {
            characterSize: metadata.characterSize,
          }),
          ...(metadata.weather && { weather: metadata.weather }),
          ...(metadata.shortDescription && {
            shortDescription: metadata.shortDescription,
          }),
          ...(metadata.isAnalyseVideo && {
            isAnalyseVideo: metadata.isAnalyseVideo,
          }),
          ...(metadata.processingTime && {
            processingTime: metadata.processingTime,
          }),
          ...(metadata.segmentDurationSeconds && {
            segmentDurationSeconds: metadata.segmentDurationSeconds,
          }),
          ...(metadata.videoSize && { videoSize: metadata.videoSize }),
          ...(metadata.environment && { environment: metadata.environment }),
          ...(metadata.groundTexture && {
            groundTexture: metadata.groundTexture,
          }),
          ...(metadata.groundMaterial && {
            groundMaterial: metadata.groundMaterial,
          }),
          ...(metadata.footwear && { footwear: metadata.footwear }),
          ...(metadata.fullPrompt && { fullPrompt: metadata.fullPrompt }),

          ...((metadata.negativePrompt ||
            metadata.seed !== undefined ||
            metadata.qualitySounds !== undefined ||
            metadata.guidenceStrength !== undefined) && {
            settings: {
              ...(metadata.negativePrompt && {
                negativePrompt: metadata.negativePrompt,
              }),
              ...(metadata.seed !== undefined && { seed: metadata.seed }),
              ...(metadata.qualitySounds !== undefined && {
                qualitySounds: metadata.qualitySounds,
              }),
              ...(metadata.guidenceStrength !== undefined && {
                guidenceStrength: metadata.guidenceStrength,
              }),
            },
          }),
        };

        updatedAnalytics[segmentKey] = segmentData;
        console.log(`✅ Updated segment data for ${segmentKey}`);
      }

      // ✅ Generate segments dari timeFrameSegment kalau belum ada
      const timeFrameSegments =
        metadata?.timeFrameSegment || lastSavedMetadata?.timeFrameSegment || [];

      const hasProvidedSegments = Object.keys(providedAnalytics).some(
        (key) =>
          key.startsWith("segment") &&
          providedAnalytics[key].startTime !== undefined
      );

      if (!hasProvidedSegments && timeFrameSegments.length > 0) {
        const timeFrameToFormattedTime = (timeFrame) => {
          if (!timeFrame) return "00:00:00.00";
          const {
            hours = 0,
            minutes = 0,
            seconds = 0,
            frames = 0,
            fps = 30,
          } = timeFrame;

          if (timeFrame.input) {
            return timeFrame.input;
          }

          // Otherwise, calculate from components
          const totalSeconds =
            hours * 3600 + minutes * 60 + seconds + frames / fps;
          const totalWholeSeconds = Math.floor(totalSeconds);
          const centiseconds = Math.round(
            (totalSeconds - totalWholeSeconds) * 100
          );

          const hh = Math.floor(totalWholeSeconds / 3600);
          const mm = Math.floor((totalWholeSeconds % 3600) / 60);
          const ss = totalWholeSeconds % 60;

          return `${hh.toString().padStart(2, "0")}:${mm
            .toString()
            .padStart(2, "0")}:${ss.toString().padStart(2, "0")}.${centiseconds
            .toString()
            .padStart(2, "0")}`;
        };

        timeFrameSegments.forEach((timeFrame, index) => {
          const segmentId = index + 1;
          const segmentKey = `segment${segmentId}`;
          let startTime = "00:00:00.00";
          let endTime = 0;

          if (index === 0) {
            startTime = "00:00:00.00";
            endTime = timeFrameToFormattedTime(timeFrame);
          } else {
            const prevTimeFrame = timeFrameSegments[index - 1];
            startTime = timeFrameToFormattedTime(prevTimeFrame);
            endTime = timeFrameToFormattedTime(timeFrame);
          }

          if (!updatedAnalytics[segmentKey]) {
            updatedAnalytics[segmentKey] = {
              segmentId,
              segmentName: `Segment ${segmentId}`,
              startTime,
              endTime,
            };
          }
        });

        const finalSegmentId = timeFrameSegments.length + 1;
        const finalSegmentKey = `segment${finalSegmentId}`;
        const lastTimeFrame = timeFrameSegments[timeFrameSegments.length - 1];

        if (!updatedAnalytics[finalSegmentKey]) {
          updatedAnalytics[finalSegmentKey] = {
            segmentId: finalSegmentId,
            segmentName: `Segment ${finalSegmentId}`,
            startTime: timeFrameToFormattedTime(lastTimeFrame),
            endTime: metadata?.totalDuration,
          };
        }
      }

      // ✅ Buat payload metadata baru
      const payloadMetaData = {
        video: videoName,
        planckName: planckName,
        saveDir: folderPath,
        savedPath: metaPath,
        savedAt: lastSavedMetadata?.savedAt || new Date().toISOString(),
        timeFrameSegment:
          metadata?.timeFrameSegment ||
          lastSavedMetadata?.timeFrameSegment ||
          [],
        segments: {
          aiVideoAnalytics: updatedAnalytics,
        },
      };

      // ✅ Simpan metadata ke file
      fs.writeFileSync(metaPath, JSON.stringify(payloadMetaData, null, 2));
      console.log("✅ Metadata saved to:", metaPath);

      // ✅ Replace lastSavedMetadata
      lastSavedMetadata = payloadMetaData;
      global.lastSavedMetadata = payloadMetaData;

      console.log("✅ Updated lastSavedMetadata and global.lastSavedMetadata");

      return {
        success: true,
        filePath,
        metaPath,
        videoFileName: videoName,
        metadata: payloadMetaData,
        mode: isMetadataOnly ? "metadata-only" : "full-save",
        segmentsCreated: Object.keys(updatedAnalytics).filter((key) =>
          key.startsWith("segment")
        ).length,
      };
    } catch (error) {
      console.error("Error saving video file and metadata:", error);
      return { success: false, message: error.message };
    }
  }
);

ipcMain.handle("get-last-saved-metadata", async () => {
  return lastSavedMetadata;
});

// Enhanced debug version of get-last-saved-metadata
// Enhanced debug version of get-last-saved-metadata

ipcMain.handle(
  "save-as-video",
  async (event, buffer, metadata, aiVideoAnalytics) => {
    try {
      let filePath = null;
      let folderPath = null;
      let metaPath = null;
      let videoName = null;
      let planckName = null;

      const isMetadataOnly = !buffer;

      const { canceled, filePath: chosenPath } = await dialog.showSaveDialog(
        win,
        {
          title: "Save Video",
          defaultPath: "My Project",
          filters: [
            { name: "Planck Files", extensions: ["*"] },
            { name: "All Files", extensions: ["*"] },
          ],
        }
      );

      if (canceled || !chosenPath) {
        return { success: false, message: "Save cancelled by user" };
      }

      const parsed = path.parse(chosenPath);
      folderPath = path.join(path.dirname(chosenPath), parsed.name);

      if (!fs.existsSync(folderPath)) {
        fs.mkdirSync(folderPath, { recursive: true });
      }

      videoName = parsed.name + ".mp4";
      planckName = parsed.name + ".planck";

      filePath = path.join(folderPath, videoName);
      metaPath = path.join(folderPath, planckName);

      // Update window title with the new project name
      updateWindowTitle(parsed.name);

      // Save video (if buffer exists)
      if (!isMetadataOnly) {
        fs.writeFileSync(filePath, buffer);
      }

      // Copy AI folder from source project if it exists
      let sourceFolderAI = null;
      const destinationFolderAI = path.join(folderPath, "ai");

      // Try to find AI folder from lastSavedMetadata location first
      if (lastSavedMetadata?.saveDir) {
        sourceFolderAI = path.join(lastSavedMetadata.saveDir, "ai");
      }

      // If not found in lastSavedMetadata, try current working directory
      if (!sourceFolderAI || !fs.existsSync(sourceFolderAI)) {
        sourceFolderAI = path.join(process.cwd(), "ai");
      }

      if (fs.existsSync(sourceFolderAI)) {
        try {
          // Copy the entire AI folder recursively
          fs.cpSync(sourceFolderAI, destinationFolderAI, {
            recursive: true,
            force: true,
          });
          console.log("✅ Successfully copied AI folder from:", sourceFolderAI);
          console.log("✅ AI folder copied to:", destinationFolderAI);

          // Rename files with new prefix
          const newPrefix = parsed.name; // e.g., "a10"
          const files = fs.readdirSync(destinationFolderAI);

          files.forEach((file) => {
            const filePath = path.join(destinationFolderAI, file);
            const stat = fs.statSync(filePath);

            // Only process files, not directories
            if (stat.isFile()) {
              // Check if file has an underscore pattern (prefix_filename)
              const underscoreIndex = file.indexOf("_");
              if (underscoreIndex > 0) {
                // Extract the part after the first underscore
                const suffixPart = file.substring(underscoreIndex);
                const newFileName = newPrefix + suffixPart;
                const newFilePath = path.join(destinationFolderAI, newFileName);

                try {
                  fs.renameSync(filePath, newFilePath);
                  console.log(`✅ Renamed: ${file} → ${newFileName}`);
                } catch (renameError) {
                  console.error(`❌ Failed to rename ${file}:`, renameError);
                }
              }
            }
          });
        } catch (copyError) {
          console.error("❌ Failed to copy AI folder:", copyError);
          // Don't throw error here, just log it and continue
        }
      } else {
        console.log("ℹ️ No AI folder found in source locations");
        console.log("ℹ️ Checked:", sourceFolderAI);
      }

      // 🔥 FIX: Properly merge aiVideoAnalytics with existing data
      const existingAnalytics =
        lastSavedMetadata?.segments?.aiVideoAnalytics || {};

      // Merge new analytics with existing ones
      const mergedAnalytics = {
        ...existingAnalytics,
        ...aiVideoAnalytics,
        destinationPath: folderPath,
      };

      const payloadMetaData = {
        ...(lastSavedMetadata || {}),
        ...(metadata || {}),
        video: videoName,
        planckName: planckName,
        saveDir: folderPath,
        savedPath: metaPath,
        segments: {
          aiVideoAnalytics: mergedAnalytics,
        },
        lastUpdated: new Date().toISOString(),
      };

      try {
        fs.writeFileSync(metaPath, JSON.stringify(payloadMetaData, null, 2));
        console.log("✅ Successfully wrote metadata to:", metaPath);
      } catch (writeError) {
        console.error("❌ Failed to write metadata:", writeError);
        throw writeError;
      }

      lastSavedMetadata = payloadMetaData;
      return {
        success: true,
        filePath,
        metaPath,
        mode: isMetadataOnly ? "metadata-only" : "full-save",
        aiFolder: fs.existsSync(destinationFolderAI) ? "copied" : "not-found",
      };
    } catch (error) {
      console.error("Error saving video file and metadata:", error);
      return { success: false, message: error.message };
    }
  }
);

// Add this new IPC handler for clearing metadata when starting a new project
ipcMain.handle("clear-project-metadata", async () => {
  try {
    // Clear the lastSavedMetadata
    lastSavedMetadata = null;

    // Reset window title to default
    updateWindowTitle();

    console.log("✅ Project metadata cleared - ready for new project");

    return {
      success: true,
      message: "Project metadata cleared successfully",
    };
  } catch (error) {
    console.error("❌ Error clearing project metadata:", error);
    return {
      success: false,
      message: error.message,
    };
  }
});

// Optional: Add a function to reset to a specific project state
ipcMain.handle("reset-project-state", async (event, newState = null) => {
  try {
    // Reset to provided state or null for completely new project
    lastSavedMetadata = newState;

    const action = newState
      ? "reset to provided state"
      : "cleared for new project";
    console.log(`✅ Project metadata ${action}`);

    return {
      success: true,
      message: `Project metadata ${action} successfully`,
      currentState: lastSavedMetadata,
    };
  } catch (error) {
    console.error("❌ Error resetting project state:", error);
    return {
      success: false,
      message: error.message,
    };
  }
});

ipcMain.handle("open-project", async (event) => {
  try {
    const { canceled, filePaths } = await dialog.showOpenDialog(win, {
      title: "Open Project",
      filters: [
        { name: "Planck Files", extensions: ["planck"] },
        { name: "All Files", extensions: ["*"] },
      ],
      properties: ["openFile"],
    });

    if (canceled || !filePaths || filePaths.length === 0) {
      return null; // Return null for cancelled operation
    }

    const projectPath = filePaths[0];
    const projectDir = path.dirname(projectPath);

    console.log("📁 Opening project:", projectPath);

    // Check if file exists
    if (!fs.existsSync(projectPath)) {
      throw new Error("Project file not found");
    }

    // Read and parse the project file
    let projectData;
    try {
      const fileContent = fs.readFileSync(projectPath, "utf8");
      console.log("📋 Raw file content length:", fileContent.length);
      projectData = JSON.parse(fileContent);
      console.log("📋 Project data loaded:", projectData.planckName);
    } catch (parseError) {
      console.error("❌ JSON parsing failed:", parseError);
      // ... error handling code remains the same ...
      throw new Error(
        `Invalid JSON format in .planck file: ${parseError.message}. Please check the file format and ensure all values are properly quoted.`
      );
    }

    // 🔥 CRITICAL FIX: Set lastSavedMetadata IMMEDIATELY and GLOBALLY
    const projectName = path.basename(projectPath, ".planck");
    const saveDir = projectData.saveDir || projectDir;

    // Create the metadata object
    const metadataObject = {
      ...projectData,
      savedPath: projectPath,
      planckName: path.basename(projectPath),
      saveDir: saveDir,
      video: projectData.video || `${projectName}.mp4`, // Ensure video filename exists
    };

    // Set it globally
    global.lastSavedMetadata = metadataObject;
    lastSavedMetadata = metadataObject;

    console.log(
      "DEBUG ✅ after open-project, lastSavedMetadata:",
      lastSavedMetadata
    );

    console.log(
      "✅ GLOBAL lastSavedMetadata set:",
      JSON.stringify(metadataObject, null, 2)
    );
    console.log("✅ LOCAL lastSavedMetadata set:", !!lastSavedMetadata);

    // Update window title with the opened project name
    updateWindowTitle(projectName);

    // Continue with the rest of your video and audio loading logic...
    // (keeping all the existing video and audio file discovery code)

    let videoBuffer = null;
    let videoPath = null;

    if (projectData.video) {
      console.log(`🎬 Looking for video file: ${projectData.video}`);

      const videoSearchPaths = [
        projectData.saveDir,
        projectDir,
        path.join(projectDir, "video"),
        path.join(projectDir, "media"),
      ].filter(Boolean);

      for (const searchPath of videoSearchPaths) {
        if (!fs.existsSync(searchPath)) continue;

        const testPath = path.join(searchPath, projectData.video);
        if (fs.existsSync(testPath)) {
          videoPath = testPath;
          console.log(`✅ Video found at: ${videoPath}`);
          break;
        }
      }

      if (!videoPath) {
        console.warn("⚠️ Video file not found, asking user to locate it");
        const videoResult = await dialog.showOpenDialog(win, {
          title: `Locate Video File: ${projectData.video}`,
          filters: [
            {
              name: "Video Files",
              extensions: ["mp4", "mov", "avi", "mkv", "webm", "m4v"],
            },
            { name: "All Files", extensions: ["*"] },
          ],
          properties: ["openFile"],
        });

        if (!videoResult.canceled && videoResult.filePaths.length > 0) {
          videoPath = videoResult.filePaths[0];
          console.log(`✅ User selected video: ${videoPath}`);
        }
      }

      if (videoPath && fs.existsSync(videoPath)) {
        try {
          videoBuffer = fs.readFileSync(videoPath);
          console.log(
            `✅ Video loaded: ${videoPath} (${videoBuffer.length} bytes)`
          );
        } catch (videoError) {
          console.error(`❌ Failed to read video file: ${videoError.message}`);
        }
      } else {
        console.warn("⚠️ Video file could not be located");
      }
    }

    // Audio file discovery code (keeping your existing logic)
    const audioFiles = {};

    if (projectData.segments?.aiVideoAnalytics) {
      console.log("🎵 Searching for audio files...");

      const audioSegments = projectData.segments.aiVideoAnalytics;
      const audioFileNames = [];

      for (const [segmentKey, segmentData] of Object.entries(audioSegments)) {
        if (segmentKey.startsWith("segment") && segmentData?.audioFile) {
          audioFileNames.push(segmentData.audioFile);
        }
      }

      if (audioFileNames.length > 0) {
        console.log(
          `🎵 Found ${audioFileNames.length} audio files to locate:`,
          audioFileNames
        );

        const audioSearchPaths = [
          audioSegments.destinationPath,
          projectData.saveDir,
          projectDir,
          audioSegments.destinationPath
            ? path.join(audioSegments.destinationPath, "ai")
            : null,
          projectData.saveDir ? path.join(projectData.saveDir, "ai") : null,
          path.join(projectDir, "ai"),
          audioSegments.destinationPath
            ? path.join(audioSegments.destinationPath, "audio")
            : null,
          projectData.saveDir ? path.join(projectData.saveDir, "audio") : null,
          path.join(projectDir, "audio"),
          path.join(projectDir, "media"),
          path.join(projectDir, "media", "audio"),
        ].filter(Boolean);

        for (const audioFileName of audioFileNames) {
          let audioPath = null;
          let found = false;

          for (const searchPath of audioSearchPaths) {
            if (!fs.existsSync(searchPath)) continue;

            const testPath = path.join(searchPath, audioFileName);
            if (fs.existsSync(testPath)) {
              try {
                const stats = fs.statSync(testPath);
                if (stats.size > 0) {
                  audioPath = testPath;
                  found = true;
                  console.log(
                    `✅ Audio file found: ${audioFileName} at ${audioPath} (${stats.size} bytes)`
                  );
                  break;
                }
              } catch (statError) {
                console.warn(
                  `⚠️ Cannot read audio file stats: ${testPath}`,
                  statError.message
                );
              }
            }
          }

          if (found && audioPath) {
            const fileUrl = `file:///${audioPath
              .replace(/\\/g, "/")
              .replace(/^\//, "")}`;
            audioFiles[audioFileName] = fileUrl;
            console.log(`🎵 Audio mapped: ${audioFileName} -> ${fileUrl}`);
          } else {
            console.warn(`❌ Audio file not found anywhere: ${audioFileName}`);
          }
        }

        console.log(
          `🎵 Audio discovery complete: ${Object.keys(audioFiles).length}/${
            audioFileNames.length
          } files found`
        );
      }
    }

    const result = {
      success: true,
      buffer: videoBuffer,
      data: projectData,
      videoName: projectData.video,
      dir: saveDir,
      videoPath: videoPath,
      audioFiles: audioFiles,
      filePath: projectPath,
    };

    // Final verification
    console.log("✅ Project loading summary:", {
      projectName: projectData.planckName,
      projectPath: projectPath,
      hasVideo: !!videoBuffer,
      videoPath: videoPath,
      saveDir: saveDir,
      lastSavedMetadataSet: !!lastSavedMetadata,
      globalLastSavedMetadataSet: !!global.lastSavedMetadata,
    });

    return result;
  } catch (error) {
    console.error("❌ Error opening project:", error);
    console.error("❌ Error stack:", error.stack);

    await dialog.showErrorBox(
      "Failed to Open Project",
      `Could not open the project file:\n\n${
        error.message || error
      }\n\nPlease check the console for more details and ensure all project files are accessible.`
    );

    return null;
  }
});

// Python
ipcMain.handle("python:run-analysis", async (event, payload) => {
  try {
    console.log("🔄 Analysis request received");

    if (!pythonServerProcess) {
      console.log("🚀 Starting Python server for analysis...");
      const serverResult = await startPythonServer();
      if (!serverResult.success) {
        return {
          success: false,
          error: "Python AI server failed to start",
          details: serverResult.error,
        };
      }
    }

    console.log("🔄 Sending analysis request to Python server...");
    console.log(
      "🚀 UNLIMITED TIMEOUT - Following Python output in real-time..."
    );

    // No timeout - let it run as long as needed
    const response = await makeHttpRequest(`${PYTHON_SERVER_URL}/analyze`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
      noTimeout: true, // Disable timeout completely for analysis requests
      onData: (chunk) => {
        // Stream real-time Python output to console
        console.log("🐍 Python output:", chunk.trim());
        // Send progress updates to renderer process
        if (win && !win.isDestroyed()) {
          win.webContents.send("python-analysis-progress", {
            type: "data",
            data: chunk.trim(),
            timestamp: new Date().toISOString(),
          });
        }
      },
    });

    if (!response.ok) {
      let errorMessage = `Server responded with status ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.detail || errorData.error || errorMessage;
      } catch (e) {
        // Response might not be JSON
      }
      throw new Error(errorMessage);
    }

    const result = await response.json();
    console.log("✅ Analysis completed successfully");

    // Send completion notification to renderer
    if (win && !win.isDestroyed()) {
      win.webContents.send("python-analysis-progress", {
        type: "complete",
        timestamp: new Date().toISOString(),
      });
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("❌ Python analysis failed:", error);

    // Send error notification to renderer
    if (win && !win.isDestroyed()) {
      win.webContents.send("python-analysis-progress", {
        type: "error",
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }

    return {
      success: false,
      error: error.message,
    };
  }
});

// Python description
ipcMain.handle("python:description", async (event, payload) => {
  try {
    console.log("🔄 Analysis request received");

    if (!pythonServerProcess) {
      console.log("🚀 Starting Python server for analysis...");
      const serverResult = await startPythonServer();
      if (!serverResult.success) {
        return {
          success: false,
          error: "Python AI server failed to start",
          details: serverResult.error,
        };
      }
    }

    console.log("🔄 Sending analysis request to Python server...");
    console.log(
      "🚀 UNLIMITED TIMEOUT - Following Python output in real-time..."
    );

    // No timeout - let it run as long as needed
    const response = await makeHttpRequest(`${PYTHON_SERVER_URL}/description`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
      noTimeout: true, // Disable timeout completely for analysis requests
      onData: (chunk) => {
        // Stream real-time Python output to console
        console.log("🐍 Python output:", chunk.trim());
        // Send progress updates to renderer process
        if (win && !win.isDestroyed()) {
          win.webContents.send("python-analysis-progress", {
            type: "data",
            data: chunk.trim(),
            timestamp: new Date().toISOString(),
          });
        }
      },
    });

    if (!response.ok) {
      let errorMessage = `Server responded with status ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.detail || errorData.error || errorMessage;
      } catch (e) {
        // Response might not be JSON
      }
      throw new Error(errorMessage);
    }

    const result = await response.json();
    console.log("✅ Analysis completed successfully");

    // Send completion notification to renderer
    if (win && !win.isDestroyed()) {
      win.webContents.send("python-analysis-progress", {
        type: "complete",
        timestamp: new Date().toISOString(),
      });
    }

    return {
      success: true,
      data: result,
    };
  } catch (error) {
    console.error("❌ Python analysis failed:", error);

    // Send error notification to renderer
    if (win && !win.isDestroyed()) {
      win.webContents.send("python-analysis-progress", {
        type: "error",
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }

    return {
      success: false,
      error: error.message,
    };
  }
});

// Check if Python server is running
ipcMain.handle("python:check-server", async () => {
  try {
    const response = await makeHttpRequest(`${PYTHON_SERVER_URL}/health`);
    if (response.ok) {
      try {
        const data = await response.json();
        return { running: true, status: data };
      } catch (e) {
        return { running: true, status: { message: "Server responding" } };
      }
    } else {
      return {
        running: false,
        error: `Server returned status ${response.status}`,
      };
    }
  } catch (error) {
    return { running: false, error: error.message };
  }
});

// Manual server start
ipcMain.handle("python:start-server", async () => {
  return await startPythonServer();
});
// Move file from one location to another
ipcMain.handle("move-file", async (event, sourcePath, destinationPath) => {
  try {
    console.log(`📁 Moving file from: ${sourcePath}`);
    console.log(`📁 Moving file to: ${destinationPath}`);

    // Check if source file exists
    if (!fs.existsSync(sourcePath)) {
      console.error(`❌ Source file does not exist: ${sourcePath}`);
      return {
        success: false,
        error: `Source file does not exist: ${sourcePath}`,
      };
    }

    // Create destination directory if it doesn't exist
    const destDir = path.dirname(destinationPath);
    if (!fs.existsSync(destDir)) {
      fs.mkdirSync(destDir, { recursive: true });
      console.log(`✅ Created destination directory: ${destDir}`);
    }

    // Copy file to new location
    fs.copyFileSync(sourcePath, destinationPath);
    console.log(`✅ File copied successfully`);

    // Delete original file
    fs.unlinkSync(sourcePath);
    console.log(`✅ Original file deleted`);

    return { success: true };
  } catch (error) {
    console.error(`❌ Failed to move file:`, error);
    return { success: false, error: error.message };
  }
});
// File system operations
ipcMain.handle("read-file-buffer", async (event, filePath) => {
  try {
    console.log(`📖 Reading file buffer: ${filePath}`);
    const buffer = fs.readFileSync(filePath);
    console.log(`✅ Read ${buffer.length} bytes from ${filePath}`);
    return buffer;
  } catch (error) {
    console.error(`❌ Error reading file ${filePath}:`, error);
    throw error;
  }
});

ipcMain.handle("write-file", async (event, filePath, data) => {
  try {
    console.log(`📝 Writing file: ${filePath}`);
    fs.writeFileSync(filePath, data);
    console.log(`✅ Successfully wrote file: ${filePath}`);
    return { success: true };
  } catch (error) {
    console.error(`❌ Error writing file ${filePath}:`, error);
    throw error;
  }
});

ipcMain.handle("file-exists", async (event, filePath) => {
  try {
    const exists = fs.existsSync(filePath);
    console.log(`🔍 File exists check for ${filePath}: ${exists}`);
    return exists;
  } catch (error) {
    console.error(`❌ Error checking file existence ${filePath}:`, error);
    return false;
  }
});

ipcMain.handle("delete-file", async (event, filePath) => {
  try {
    console.log(`🗑️ Deleting file: ${filePath}`);

    // Check if file exists first
    if (!fs.existsSync(filePath)) {
      console.warn(`⚠️ File does not exist: ${filePath}`);
      return { success: false, error: "File does not exist" };
    }

    // Delete the file
    fs.unlinkSync(filePath);
    console.log(`✅ Successfully deleted file: ${filePath}`);

    return { success: true };
  } catch (error) {
    console.error(`❌ Error deleting file ${filePath}:`, error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle("write-file-buffer", async (event, filePath, buffer) => {
  try {
    console.log(`📝 Writing buffer file: ${filePath}`);

    // Ensure the directory exists
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Write the buffer to file
    fs.writeFileSync(filePath, Buffer.from(buffer));
    console.log(`✅ Successfully wrote buffer to file: ${filePath}`);

    return { success: true };
  } catch (error) {
    console.error(`❌ Error writing buffer file ${filePath}:`, error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle("read-audio-file", async (event, filePath) => {
  try {
    console.log(`🎵 Reading audio file: ${filePath}`);

    // Check if file exists first
    if (!fs.existsSync(filePath)) {
      throw new Error(`Audio file not found: ${filePath}`);
    }

    // Get file stats to check size
    const stats = fs.statSync(filePath);
    console.log(`🎵 Audio file size: ${stats.size} bytes`);

    if (stats.size === 0) {
      throw new Error(`Audio file is empty: ${filePath}`);
    }

    // Read the file as buffer
    const buffer = fs.readFileSync(filePath);
    console.log(
      `✅ Successfully read audio file: ${filePath} (${buffer.length} bytes)`
    );

    return buffer;
  } catch (error) {
    console.error(`❌ Error reading audio file ${filePath}:`, error);
    throw new Error(`Failed to read audio file: ${error.message}`);
  }
});

// Enhanced file existence check with detailed logging
ipcMain.handle("check-file-exists", async (event, filePath) => {
  try {
    const exists = fs.existsSync(filePath);
    const stats = exists ? fs.statSync(filePath) : null;

    console.log(`🔍 File check: ${filePath}`);
    console.log(`   Exists: ${exists}`);
    if (stats) {
      console.log(`   Size: ${stats.size} bytes`);
      console.log(
        `   Type: ${
          stats.isFile() ? "file" : stats.isDirectory() ? "directory" : "other"
        }`
      );
    }

    return {
      exists,
      size: stats ? stats.size : 0,
      isFile: stats ? stats.isFile() : false,
      isDirectory: stats ? stats.isDirectory() : false,
    };
  } catch (error) {
    console.error(`❌ Error checking file ${filePath}:`, error);
    return {
      exists: false,
      error: error.message,
    };
  }
});

// List files in directory with filtering
ipcMain.handle(
  "list-directory-files",
  async (event, dirPath, extensions = []) => {
    try {
      console.log(`📁 Listing files in: ${dirPath}`);

      if (!fs.existsSync(dirPath)) {
        return { success: false, error: "Directory does not exist", files: [] };
      }

      const items = fs.readdirSync(dirPath, { withFileTypes: true });
      const files = items
        .filter((item) => item.isFile())
        .map((item) => {
          const filePath = path.join(dirPath, item.name);
          const stats = fs.statSync(filePath);
          return {
            name: item.name,
            path: filePath,
            size: stats.size,
            extension: path.extname(item.name).toLowerCase(),
          };
        });

      // Filter by extensions if provided
      const filteredFiles =
        extensions.length > 0
          ? files.filter((file) => extensions.includes(file.extension))
          : files;

      console.log(`✅ Found ${filteredFiles.length} files in ${dirPath}`);

      return {
        success: true,
        files: filteredFiles,
        totalFiles: files.length,
        filteredFiles: filteredFiles.length,
      };
    } catch (error) {
      console.error(`❌ Error listing directory ${dirPath}:`, error);
      return {
        success: false,
        error: error.message,
        files: [],
      };
    }
  }
);

// Audio file validation handler
ipcMain.handle("validate-audio-file", async (event, filePath) => {
  try {
    console.log(`🎵 Validating audio file: ${filePath}`);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return {
        valid: false,
        error: "File does not exist",
      };
    }

    // Get file stats
    const stats = fs.statSync(filePath);

    // Check if it's a file (not directory)
    if (!stats.isFile()) {
      return {
        valid: false,
        error: "Path is not a file",
      };
    }

    // Check file size
    if (stats.size === 0) {
      return {
        valid: false,
        error: "File is empty",
      };
    }

    // Check file extension
    const ext = path.extname(filePath).toLowerCase();
    const audioExtensions = [".wav", ".mp3", ".ogg", ".m4a", ".aac", ".flac"];

    if (!audioExtensions.includes(ext)) {
      return {
        valid: false,
        error: `Unsupported audio format: ${ext}`,
      };
    }

    // Try to read first few bytes to verify it's not corrupted
    try {
      const buffer = fs.readFileSync(filePath, { start: 0, end: 100 });
      if (buffer.length === 0) {
        return {
          valid: false,
          error: "Cannot read file content",
        };
      }
    } catch (readError) {
      return {
        valid: false,
        error: `Cannot read file: ${readError.message}`,
      };
    }

    console.log(`✅ Audio file validation passed: ${filePath}`);

    return {
      valid: true,
      size: stats.size,
      extension: ext,
      path: filePath,
    };
  } catch (error) {
    console.error(`❌ Error validating audio file ${filePath}:`, error);
    return {
      valid: false,
      error: error.message,
    };
  }
});

// Show save dialog and return selected path
ipcMain.handle("show-save-dialog", async (event, options) => {
  try {
    const { canceled, filePath } = await dialog.showSaveDialog(win, options);
    if (canceled || !filePath) {
      return { canceled: true, filePath: null };
    }
    return { canceled: false, filePath };
  } catch (error) {
    console.error("❌ Error showing save dialog:", error);
    return { canceled: true, filePath: null, error: error.message };
  }
});

// IPC handler to update window title from renderer process
ipcMain.handle("update-window-title", async (event, projectTitle) => {
  try {
    updateWindowTitle(projectTitle);
    return { success: true, title: projectTitle };
  } catch (error) {
    console.error("❌ Error updating window title:", error);
    return { success: false, error: error.message };
  }
});

// IPC handler to get current window title
ipcMain.handle("get-window-title", async () => {
  try {
    return {
      success: true,
      title: currentProjectTitle,
      fullTitle:
        mainWindow && !mainWindow.isDestroyed() ? mainWindow.getTitle() : null,
    };
  } catch (error) {
    console.error("❌ Error getting window title:", error);
    return { success: false, error: error.message };
  }
});

// IPC handler to update save menu items state
ipcMain.handle("update-save-menu-items", async (event, enabled) => {
  try {
    updateSaveMenuItems(enabled);
    return { success: true, enabled };
  } catch (error) {
    console.error("❌ Error updating save menu items:", error);
    return { success: false, error: error.message };
  }
});

// Convert WAV to aaf using Python API
ipcMain.handle("convert-wav-to-aaf", async (_event, wavPath) => {
  try {
    console.log(`🎵 Converting WAV to aaf: ${wavPath}`);

    if (!pythonServerProcess) {
      console.log("🚀 Starting Python server for conversion...");
      const serverResult = await startPythonServer();
      if (!serverResult.success) {
        return {
          success: false,
          error: "Python AI server failed to start",
          details: serverResult.error,
        };
      }
    }

    console.log("🔄 Sending WAV to AAF conversion request to Python server...");

    const response = await makeHttpRequest(
      `${PYTHON_SERVER_URL}/convert/wav-to-aaf`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ wav_path: wavPath }),
        timeout: 30000, // 30 second timeout for conversion
      }
    );

    if (!response.ok) {
      let errorMessage = `Server responded with status ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.detail || errorData.message || errorMessage;
      } catch (e) {
        // Response might not be JSON
      }
      throw new Error(errorMessage);
    }

    const result = await response.json();
    console.log("✅ WAV to AAF conversion completed successfully");

    return {
      success: result.success,
      aafPath: result.aaf_path,
      message: result.message,
    };
  } catch (error) {
    console.error("❌ WAV to AAF conversion failed:", error);
    return {
      success: false,
      error: error.message,
    };
  }
});

// Helper function to get video duration
const getVideoDuration = (videoPath) => {
  return new Promise((resolve, reject) => {
    const ffprobe = spawn("ffprobe", [
      "-v",
      "quiet",
      "-show_entries",
      "format=duration",
      "-of",
      "csv=p=0",
      videoPath,
    ]);

    let stdout = "";
    let stderr = "";

    ffprobe.stdout.on("data", (data) => {
      stdout += data.toString();
    });

    ffprobe.stderr.on("data", (data) => {
      stderr += data.toString();
    });

    ffprobe.on("close", (code) => {
      if (code === 0) {
        const duration = parseFloat(stdout.trim());
        resolve(duration);
      } else {
        reject(new Error(`ffprobe failed: ${stderr}`));
      }
    });
  });
};

// Combine video and audio using FFmpeg
ipcMain.handle(
  "combine-video-audio",
  async (
    _event,
    videoPath,
    audioSegments,
    outputPath,
    useVideoAudio = false,
    videoTrimming = null
  ) => {
    try {
      console.log(`🎬 Combining video with audio:`);
      console.log(`  Video: ${videoPath}`);
      console.log(`  Use video audio: ${useVideoAudio}`);
      console.log(`  Audio segments: ${audioSegments.length}`);
      console.log(`  Output: ${outputPath}`);
      console.log(`  Video trimming:`, videoTrimming);

      // Log all audio segments
      audioSegments.forEach((segment, index) => {
        console.log(
          `  Audio ${index + 1}: starts at ${segment.startTime}s, duration ${
            segment.duration
          }s, file: ${segment.filePath}`
        );
      });

      // Check if video file exists
      if (!fs.existsSync(videoPath)) {
        throw new Error(`Video file not found: ${videoPath}`);
      }

      // Check if all audio files exist (only if not using video audio)
      if (!useVideoAudio) {
        for (const segment of audioSegments) {
          if (!fs.existsSync(segment.filePath)) {
            throw new Error(`Audio file not found: ${segment.filePath}`);
          }
        }
      }

      // Get video duration first
      const videoDuration = await getVideoDuration(videoPath);
      console.log(`📏 Video duration: ${videoDuration} seconds`);

      // Determine actual duration to use based on trimming
      let actualDuration = videoDuration;
      let videoInputArgs = ["-i", videoPath];

      if (videoTrimming && videoTrimming.startTime !== undefined) {
        console.log(
          `✂️ Applying video trimming from ${videoTrimming.startTime}s`
        );
        videoInputArgs = [
          "-ss",
          videoTrimming.startTime.toString(),
          "-i",
          videoPath,
        ];

        if (
          videoTrimming.duration !== null &&
          videoTrimming.duration !== undefined
        ) {
          actualDuration = videoTrimming.duration;
          console.log(`✂️ Trimmed duration: ${actualDuration}s`);
        } else {
          actualDuration = videoDuration - videoTrimming.startTime;
          console.log(`✂️ Trimmed duration: ${actualDuration}s (to end)`);
        }
      }

      // Build FFmpeg command
      let ffmpegCommand;
      if (useVideoAudio) {
        // Use original video audio (simple copy)
        console.log("📻 Using original video audio");
        ffmpegCommand = [
          "-y", // Overwrite output file
          ...videoInputArgs, // Use video input args (includes -ss if trimming)
          "-c:v",
          "copy", // Copy video stream without re-encoding
          "-c:a",
          "aac", // Re-encode audio as AAC for consistency
          "-t",
          actualDuration.toString(), // Use actual duration (trimmed or full)
          outputPath,
        ];
      } else if (audioSegments.length > 0) {
        // Build input arguments for all audio files
        const inputArgs = ["-y", ...videoInputArgs]; // Video input first (with trimming)

        // Add all audio inputs
        audioSegments.forEach((segment) => {
          inputArgs.push("-i", segment.filePath);
        });

        // Build filter complex for positioning all audio segments with proper duration
        let filterComplex = `anullsrc=channel_layout=stereo:sample_rate=44100:duration=${actualDuration}[silence]`; // Base silence with actual duration

        // Add each audio segment with proper timing and duration limits
        audioSegments.forEach((segment, index) => {
          const delayMs = Math.round(segment.startTime * 1000);
          const durationSec = segment.duration; // Duration in seconds
          const inputIndex = index + 1; // +1 because video is input 0

          // First trim the audio to the correct duration, then delay it
          filterComplex += `;[${inputIndex}:a]atrim=duration=${durationSec},adelay=${delayMs}|${delayMs}[delayed${index}]`;

          console.log(
            `  🎵 Audio ${
              index + 1
            }: trimmed to ${durationSec}s, delayed by ${delayMs}ms`
          );
        });

        // Mix all delayed and trimmed audio segments with silence
        const audioInputs = audioSegments
          .map((_, index) => `[delayed${index}]`)
          .join("");
        filterComplex += `;[silence]${audioInputs}amix=inputs=${
          audioSegments.length + 1
        }:duration=first:dropout_transition=0[final_audio]`;

        ffmpegCommand = [
          ...inputArgs,
          "-c:v",
          "copy", // Copy video stream without re-encoding
          "-c:a",
          "aac", // Encode audio as AAC
          "-filter_complex",
          filterComplex,
          "-map",
          "0:v:0", // Use video from first input
          "-map",
          "[final_audio]", // Use final mixed audio
          "-t",
          actualDuration.toString(), // Use actual duration (trimmed or full)
          outputPath,
        ];
      } else {
        // Video only (no audio segments)
        console.log("No audio segments provided - creating video-only output");
        ffmpegCommand = [
          "-y", // Overwrite output file
          ...videoInputArgs, // Use video input args (includes -ss if trimming)
          "-c:v",
          "copy", // Copy video stream without re-encoding
          "-an", // No audio
          "-t",
          actualDuration.toString(), // Use actual duration (trimmed or full)
          outputPath,
        ];
      }

      console.log("🔄 Starting FFmpeg process...");
      console.log("Command:", "ffmpeg", ffmpegCommand.join(" "));

      return new Promise((resolve, reject) => {
        const ffmpeg = spawn("ffmpeg", ffmpegCommand);

        let stderr = "";

        ffmpeg.stderr.on("data", (data) => {
          stderr += data.toString();
          // Log progress information
          const progress = data.toString();
          if (progress.includes("time=")) {
            console.log("FFmpeg progress:", progress.trim());
          }
        });

        ffmpeg.on("close", (code) => {
          if (code === 0) {
            console.log("✅ Video combination completed successfully");

            // Verify output file exists and has content
            if (fs.existsSync(outputPath)) {
              const stats = fs.statSync(outputPath);
              console.log(
                `📊 Output file size: ${(stats.size / 1024 / 1024).toFixed(
                  2
                )} MB`
              );

              resolve({
                success: true,
                outputPath: outputPath,
                fileSize: stats.size,
                message: "Video and audio combined successfully",
              });
            } else {
              reject(new Error("Output file was not created"));
            }
          } else {
            console.error(`❌ FFmpeg process exited with code ${code}`);
            console.error("FFmpeg stderr:", stderr);
            reject(new Error(`FFmpeg failed with code ${code}: ${stderr}`));
          }
        });

        ffmpeg.on("error", (error) => {
          console.error("❌ Failed to start FFmpeg process:", error);
          reject(new Error(`Failed to start FFmpeg: ${error.message}`));
        });
      });
    } catch (error) {
      console.error("❌ Video combination failed:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
);

// Search for audio files in project directories
ipcMain.handle(
  "find-audio-files",
  async (_event, searchPaths, audioFileNames) => {
    try {
      console.log(`🔍 Searching for audio files:`, audioFileNames);
      console.log(`🔍 Search paths:`, searchPaths);

      const foundFiles = {};
      const audioExtensions = [".wav", ".mp3", ".ogg", ".m4a", ".aac", ".flac"];

      for (const fileName of audioFileNames) {
        let found = false;

        // Try exact filename first
        for (const searchPath of searchPaths) {
          if (!fs.existsSync(searchPath)) continue;

          const fullPath = path.join(searchPath, fileName);
          if (fs.existsSync(fullPath)) {
            foundFiles[fileName] = fullPath;
            found = true;
            console.log(`✅ Found exact match: ${fileName} at ${fullPath}`);
            break;
          }
        }

        // If not found, try with different extensions
        if (!found) {
          const baseName = path.parse(fileName).name;

          for (const searchPath of searchPaths) {
            if (!fs.existsSync(searchPath)) continue;

            for (const ext of audioExtensions) {
              const testName = baseName + ext;
              const fullPath = path.join(searchPath, testName);

              if (fs.existsSync(fullPath)) {
                foundFiles[fileName] = fullPath;
                found = true;
                console.log(
                  `✅ Found with extension change: ${fileName} -> ${testName} at ${fullPath}`
                );
                break;
              }
            }

            if (found) break;
          }
        }

        if (!found) {
          console.warn(`⚠️ Audio file not found: ${fileName}`);
        }
      }

      return {
        success: true,
        foundFiles,
        totalRequested: audioFileNames.length,
        totalFound: Object.keys(foundFiles).length,
      };
    } catch (error) {
      console.error(`❌ Error searching for audio files:`, error);
      return {
        success: false,
        error: error.message,
        foundFiles: {},
      };
    }
  }
);

// Get file path from File object (Electron-specific)
ipcMain.handle("get-file-path", async (_event, fileName) => {
  try {
    // In Electron, we can try to get the file path from recently accessed files
    // or provide a way to map file names to paths
    console.log("Requested file path for:", fileName);

    // For now, return null - we'll implement file path storage later
    return {
      success: false,
      error: "File path lookup not implemented yet",
    };
  } catch (error) {
    console.error("❌ Error getting file path:", error);
    return {
      success: false,
      error: error.message,
    };
  }
});

// Select video file for auto cut
ipcMain.handle("select-video-file", async (_event) => {
  try {
    const { canceled, filePaths } = await dialog.showOpenDialog(win, {
      title: "Select Video File for Auto Cut",
      filters: [
        {
          name: "Video Files",
          extensions: ["mp4", "mov", "avi", "mkv", "webm", "m4v", "flv", "wmv"],
        },
        { name: "All Files", extensions: ["*"] },
      ],
      properties: ["openFile"],
    });

    if (canceled || !filePaths || filePaths.length === 0) {
      return {
        success: false,
        error: "No file selected",
      };
    }

    return {
      success: true,
      filePath: filePaths[0],
    };
  } catch (error) {
    console.error("❌ Error selecting video file:", error);
    return {
      success: false,
      error: error.message,
    };
  }
});

// Auto cut video using Python API
ipcMain.handle("video-auto-cut", async (_event, config) => {
  try {
    console.log(`🎬 Starting video auto cut with config:`, config);

    if (!pythonServerProcess) {
      console.log("🚀 Starting Python server for auto cut...");
      const serverResult = await startPythonServer();
      if (!serverResult.success) {
        return {
          success: false,
          error: "Python AI server failed to start",
          details: serverResult.error,
        };
      }
    }

    console.log("🔄 Sending auto cut request to Python server...");

    const response = await makeHttpRequest(
      `${PYTHON_SERVER_URL}/video-auto-cut`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(config),
        timeout: 120000, // Increased to 120 second timeout for auto cut (large videos need more time)
      }
    );

    if (!response.ok) {
      let errorMessage = `Server responded with status ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage =
          errorData.detail ||
          errorData.error_msg ||
          errorData.message ||
          errorMessage;
      } catch (e) {
        // Response might not be JSON
      }
      throw new Error(errorMessage);
    }

    const result = await response.json();
    console.log("✅ Video auto cut completed successfully");

    // Handle the new API response format that wraps data in success/data structure
    if (result.success && result.data) {
      return {
        success: true,
        data: result.data,
      };
    } else {
      return {
        success: false,
        err_msg: result.error_msg,
      };
    }
  } catch (error) {
    console.error("❌ Video auto cut failed:", error);
    return {
      success: false,
      error: error.message,
    };
  }
});
