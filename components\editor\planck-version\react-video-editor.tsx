/* eslint-disable react-hooks/exhaustive-deps */
"use client";

// UI Components
import { SidebarInset } from "@/components/ui/sidebar";
import { AppSidebar } from "./components/sidebar/app-sidebar";
import { Editor } from "./components/core/editor";
import { SidebarProvider as UISidebarProvider } from "@/components/ui/sidebar";
import { SidebarProvider as EditorSidebarProvider } from "./contexts/sidebar-context";
// Context Providers
import { EditorProvider } from "./contexts/editor-context";
import AppProvider from "./contexts/AppProvider";
// Custom Hooks
import { useOverlays } from "./hooks/use-overlays";
import { useVideoPlayer } from "./hooks/use-video-player";
import { useTimelineClick } from "./hooks/use-timeline-click";
import { useAspectRatio } from "./hooks/use-aspect-ratio";
import { useCompositionDuration } from "./hooks/use-composition-duration";
import { useHistory } from "./hooks/use-history";

// Types
import {
  Overlay,
  OverlayType,
  ClipOverlay,
  SoundOverlay,
  ImageOverlay,
} from "./types";
import { useRendering } from "./hooks/use-rendering";
import { FPS, RENDER_TYPE } from "./constants";
import { getVideoMetadata } from "./utils/media-upload";
import { getEffectiveFPS } from "./utils/fps-utils";
import { TimelineProvider } from "./contexts/timeline-context";

// Components
import { useState, useEffect, useCallback, useMemo } from "react";
import { LocalMediaProvider } from "./contexts/local-media-context";
import { KeyframeProvider } from "./contexts/keyframe-context";
import { AssetLoadingProvider } from "./contexts/asset-loading-context";
import ImportMarkersModal from "./components/modal/ImportMarkersModal";
import ExportModal from "./components/modal/ExportModal";
import ExportMarkersModal from "./components/modal/ExportMarkersModal";
import { useCutStore } from "@/store/cutStore";

// File processing functionality
const useFileProcessing = (currentOverlays: Overlay[]) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);

  const createVideoOverlay = useCallback(
    async (file: File): Promise<ClipOverlay> => {
      return new Promise(async (resolve) => {
        const metadata = await getVideoMetadata(file);

        if (metadata) {
          const { duration, fps: videoFps, width, height } = metadata;
          const url = URL.createObjectURL(file);

          const overlay: ClipOverlay = {
            id: Math.floor(Date.now() + Math.random() * 1000),
            type: OverlayType.VIDEO,
            content: file.name,
            src: url,
            durationInFrames: Math.round(duration * videoFps),
            from: 0,
            height: height || 1080,
            width: width || 1920,
            row: 0,
            left: 0,
            top: 0,
            isDragging: false,
            rotation: 0,
            videoStartTime: 0,
            speed: 1,
            fps: videoFps,
            styles: {
              opacity: 1,
              zIndex: 1,
              objectFit: "contain",
              objectPosition: "center",
              volume: 1,
              borderRadius: "0px",
              filter: "none",
            },
          };

          resolve(overlay);
        } else {
          // ... existing fallback code (remove the broken debug logs)
          const video = document.createElement("video");
          const url = URL.createObjectURL(file);

          video.onloadedmetadata = async () => {
            try {
              // Extract accurate metadata including FPS and duration
              const metadata = await getVideoMetadata(file);
              const duration = metadata?.duration || video.duration;
              const fps = metadata?.fps || FPS;
              const durationInFrames = Math.round(duration * fps);

              const overlay: ClipOverlay = {
                id: Math.floor(Date.now() + Math.random() * 1000),
                type: OverlayType.VIDEO,
                content: file.name,
                src: url,
                durationInFrames,
                from: 0,
                height: metadata?.height || video.videoHeight || 1080,
                width: metadata?.width || video.videoWidth || 1920,
                row: 0,
                left: 0,
                top: 0,
                isDragging: false,
                rotation: 0,
                videoStartTime: 0,
                speed: 1,
                fps: fps,
                styles: {
                  opacity: 1,
                  zIndex: 1,
                  objectFit: "contain",
                  objectPosition: "center",
                  volume: 1,
                  borderRadius: "0px",
                  filter: "none",
                },
              };

              resolve(overlay);
            } catch (error) {
              console.error("Error extracting video metadata:", error);
              // Fallback to basic video element metadata
              const duration = video.duration;
              const durationInFrames = Math.round(duration * FPS);

              const overlay: ClipOverlay = {
                id: Math.floor(Date.now() + Math.random() * 1000),
                type: OverlayType.VIDEO,
                content: file.name,
                src: url,
                durationInFrames,
                from: 0,
                height: video.videoHeight || 1080,
                width: video.videoWidth || 1920,
                row: 0,
                left: 0,
                top: 0,
                isDragging: false,
                rotation: 0,
                videoStartTime: 0,
                speed: 1,
                fps: FPS,
                styles: {
                  opacity: 1,
                  zIndex: 1,
                  objectFit: "contain",
                  objectPosition: "center",
                  volume: 1,
                  borderRadius: "0px",
                  filter: "none",
                },
              };

              resolve(overlay);
            }
          };

          video.onerror = () => {
            const overlay: ClipOverlay = {
              id: Math.floor(Date.now() + Math.random() * 1000),
              type: OverlayType.VIDEO,
              content: file.name,
              src: url,
              durationInFrames: 900,
              from: 0,
              height: 1080,
              width: 1920,
              row: 0,
              left: 0,
              top: 0,
              isDragging: false,
              rotation: 0,
              videoStartTime: 0,
              speed: 1,
              fps: FPS,
              styles: {
                opacity: 1,
                zIndex: 1,
                objectFit: "contain",
                objectPosition: "center",
                volume: 1,
                borderRadius: "0px",
                filter: "none",
              },
            };

            resolve(overlay);
          };

          video.src = url;
        }
      });
    },
    []
  );

  const createAudioOverlay = useCallback(
    async (file: File): Promise<SoundOverlay> => {
      return new Promise((resolve) => {
        const audio = document.createElement("audio");
        const url = URL.createObjectURL(file);

        audio.onloadedmetadata = () => {
          const duration = audio.duration;
          // FIX: Use getEffectiveFPS with currentOverlays parameter
          const effectiveFPS = getEffectiveFPS(currentOverlays);
          const durationInFrames = Math.floor(duration * effectiveFPS);

          const overlay: SoundOverlay = {
            id: Math.floor(Date.now() + Math.random() * 1000),
            type: OverlayType.SOUND,
            content: file.name,
            src: url,
            durationInFrames,
            from: 0,
            height: 0,
            width: 0,
            row: 1,
            left: 0,
            top: 0,
            isDragging: false,
            rotation: 0,
            startFromSound: 0,
            styles: {
              opacity: 1,
              zIndex: 1,
              volume: 1,
            },
          };

          resolve(overlay);
        };

        audio.onerror = () => {
          const effectiveFPS = getEffectiveFPS(currentOverlays);
          const overlay: SoundOverlay = {
            id: Math.floor(Date.now() + Math.random() * 1000),
            type: OverlayType.SOUND,
            content: file.name,
            src: url,
            durationInFrames: Math.round(150 * (effectiveFPS / FPS)), // Scale fallback
            from: 0,
            height: 0,
            width: 0,
            row: 1,
            left: 0,
            top: 0,
            isDragging: false,
            rotation: 0,
            startFromSound: 0,
            styles: {
              opacity: 1,
              zIndex: 1,
              volume: 1,
            },
          };

          resolve(overlay);
        };

        audio.src = url;
      });
    },
    [currentOverlays] // Add currentOverlays as dependency
  );

  const createImageOverlay = useCallback(
    async (file: File): Promise<ImageOverlay> => {
      return new Promise((resolve) => {
        const img = document.createElement("img");
        const url = URL.createObjectURL(file);

        img.onload = () => {
          const overlay: ImageOverlay = {
            id: Math.floor(Date.now() + Math.random() * 1000),
            type: OverlayType.IMAGE,
            src: url,
            content: file.name,
            durationInFrames: 150,
            from: 0,
            height: img.naturalHeight,
            width: img.naturalWidth,
            row: 2,
            left: 0,
            top: 0,
            isDragging: false,
            rotation: 0,
            styles: {
              opacity: 1,
              zIndex: 1,
              filter: "none",
              borderRadius: "0px",
              objectFit: "contain",
              objectPosition: "center",
              boxShadow: "none",
              border: "none",
            },
          };

          resolve(overlay);
        };

        img.onerror = () => {
          const overlay: ImageOverlay = {
            id: Math.floor(Date.now() + Math.random() * 1000),
            type: OverlayType.IMAGE,
            src: url,
            content: file.name,
            durationInFrames: 150,
            from: 0,
            height: 1080,
            width: 1920,
            row: 2,
            left: 0,
            top: 0,
            isDragging: false,
            rotation: 0,
            styles: {
              opacity: 1,
              zIndex: 1,
              filter: "none",
              borderRadius: "0px",
              objectFit: "contain",
              objectPosition: "center",
              boxShadow: "none",
              border: "none",
            },
          };

          resolve(overlay);
        };

        img.src = url;
      });
    },
    []
  );

  const processFiles = useCallback(
    async (files: File[]): Promise<Overlay[]> => {
      setIsProcessing(true);
      setProcessingProgress(0);

      const overlays: Overlay[] = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const extension = "." + file.name.split(".").pop()?.toLowerCase();

        try {
          let overlay: Overlay;

          if (
            [".mp4", ".mov", ".avi", ".mkv", ".webm", ".m4v"].includes(
              extension
            )
          ) {
            overlay = await createVideoOverlay(file);
          } else if (
            [".mp3", ".wav", ".aac", ".m4a", ".ogg", ".flac"].includes(
              extension
            )
          ) {
            overlay = await createAudioOverlay(file);
          } else if (
            [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"].includes(
              extension
            )
          ) {
            overlay = await createImageOverlay(file);
          } else {
            continue;
          }

          overlays.push(overlay);
          setProcessingProgress(((i + 1) / files.length) * 100);
        } catch (error) {
          console.error(`Error processing file ${file.name}:`, error);
        }
      }

      setIsProcessing(false);
      setProcessingProgress(0);

      return overlays;
    },
    [createVideoOverlay, createAudioOverlay, createImageOverlay]
  );

  return {
    processFiles,
    isProcessing,
    processingProgress,
  };
};

export default function ReactVideoEditor({ projectId }: { projectId: string }) {
  const [playbackRate, setPlaybackRate] = useState(1);
  const [localCurrentFrame, setLocalCurrentFrame] = useState(0);
  const cutTimes = useCutStore((state) => state.cutTimes);

  // Note: projectId is passed as prop but not used in current implementation

  const {
    overlays,
    setOverlays,
    selectedOverlayId,
    setSelectedOverlayId,
    changeOverlay,
    addOverlay,
    deleteOverlay,
    duplicateOverlay,
    splitOverlay,
    deleteOverlaysByRow,
    updateOverlayStyles,
    resetOverlays,
  } = useOverlays([]);
  const { isProcessing, processingProgress } = useFileProcessing(overlays);
  const currentFPS = getEffectiveFPS(overlays);
  const { durationInFrames, durationInSeconds } =
    useCompositionDuration(overlays);

  const { isPlaying, playerRef, togglePlayPause, seekTo } = useVideoPlayer(
    overlays,
    durationInFrames
  );

  const {
    aspectRatio,
    setAspectRatio,
    playerDimensions,
    updatePlayerDimensions,
    getAspectRatioDimensions,
    scaleFactor,
    updateScaleFactor,
    getScaleFactor,
  } = useAspectRatio();

  const handleFilesAdded = useCallback(
    async (files: File[]) => {
      if (files.length === 0) return;

      try {
        for (const file of files) {
          const url = URL.createObjectURL(file);
          const extension = "." + file.name.split(".").pop()?.toLowerCase();

          let fileType: "video" | "audio" | "image" | null = null;
          if (
            [".mp4", ".mov", ".avi", ".mkv", ".webm", ".m4v"].includes(
              extension
            )
          ) {
            fileType = "video";
          } else if (
            [".mp3", ".wav", ".aac", ".m4a", ".ogg", ".flac"].includes(
              extension
            )
          ) {
            fileType = "audio";
          } else if (
            [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"].includes(
              extension
            )
          ) {
            fileType = "image";
          }

          if (!fileType) continue;

          const { width, height } = getAspectRatioDimensions();

          const existingOverlays = overlays.filter((o) => o.row === 0);
          const lastOverlay =
            existingOverlays.length > 0
              ? existingOverlays.sort(
                  (a, b) =>
                    b.from + b.durationInFrames - (a.from + a.durationInFrames)
                )[0]
              : null;
          const from = lastOverlay
            ? lastOverlay.from + lastOverlay.durationInFrames + 5
            : 0;

          if (fileType === "video") {
            const metadata = await getVideoMetadata(file);

            if (metadata) {
              const { duration, fps: videoFps } = metadata;

              // 🔥 VERIFY: Check if this matches what the player expects
              const newOverlay: ClipOverlay = {
                left: 0,
                top: 0,
                width,
                height,
                durationInFrames: Math.round(duration * videoFps),
                from,
                id: Date.now() + Math.random() * 1000,
                rotation: 0,
                row: 0,
                isDragging: false,
                type: OverlayType.VIDEO,
                content: file.name,
                src: url,
                videoStartTime: 0,
                speed: 1,
                fps: videoFps,
                styles: {
                  opacity: 1,
                  zIndex: 100,
                  objectFit: "contain",
                  objectPosition: "center",
                  volume: 1,
                },
              };
              addOverlay(newOverlay);
            } else {
              const video = document.createElement("video");
              video.onloadedmetadata = () => {
                const newOverlay: ClipOverlay = {
                  left: 0,
                  top: 0,
                  width,
                  height,
                  durationInFrames: Math.round(video.duration * FPS),
                  from,
                  id: Date.now() + Math.random() * 1000,
                  rotation: 0,
                  row: 0,
                  isDragging: false,
                  type: OverlayType.VIDEO,
                  content: file.name,
                  src: url,
                  videoStartTime: 0,
                  speed: 1,
                  fps: FPS,
                  styles: {
                    opacity: 1,
                    zIndex: 100,
                    objectFit: "contain",
                    objectPosition: "center",
                    volume: 1,
                  },
                };
                addOverlay(newOverlay);
              };
              video.onerror = () => {
                const newOverlay: ClipOverlay = {
                  left: 0,
                  top: 0,
                  width,
                  height,
                  durationInFrames: 150,
                  from,
                  id: Date.now() + Math.random() * 1000,
                  rotation: 0,
                  row: 0,
                  isDragging: false,
                  type: OverlayType.VIDEO,
                  content: file.name,
                  src: url,
                  videoStartTime: 0,
                  speed: 1,
                  fps: FPS,
                  styles: {
                    opacity: 1,
                    zIndex: 100,
                    objectFit: "contain",
                    objectPosition: "center",
                    volume: 1,
                  },
                };
                addOverlay(newOverlay);
              };
              video.src = url;
            }
          } else if (fileType === "audio") {
            const audio = document.createElement("audio");
            audio.onloadedmetadata = () => {
              // FIX: Use currentFPS instead of fixed FPS
              const newOverlay: SoundOverlay = {
                left: 0,
                top: 0,
                width: 0,
                height: 0,
                durationInFrames: Math.round(audio.duration * currentFPS),
                from,
                id: Date.now() + Math.random() * 1000,
                rotation: 0,
                row: 1,
                isDragging: false,
                type: OverlayType.SOUND,
                content: file.name,
                src: url,
                startFromSound: 0,
                styles: {
                  volume: 1,
                },
              };
              addOverlay(newOverlay);
            };
            audio.onerror = () => {
              // FIX: Use currentFPS here too
              const newOverlay: SoundOverlay = {
                left: 0,
                top: 0,
                width: 0,
                height: 0,
                durationInFrames: 150, // Or use a calculated value based on currentFPS
                from,
                id: Date.now() + Math.random() * 1000,
                rotation: 0,
                row: 1,
                isDragging: false,
                type: OverlayType.SOUND,
                content: file.name,
                src: url,
                startFromSound: 0,
                styles: {
                  volume: 1,
                },
              };
              addOverlay(newOverlay);
            };
            audio.src = url;
          } else if (fileType === "image") {
            const newOverlay: ImageOverlay = {
              left: 0,
              top: 0,
              width,
              height,
              durationInFrames: 150,
              from,
              id: Date.now() + Math.random() * 1000,
              rotation: 0,
              row: 2,
              isDragging: false,
              type: OverlayType.IMAGE,
              src: url,
              content: file.name,
              styles: {
                opacity: 1,
                objectFit: "contain",
                objectPosition: "center",
              },
            };
            addOverlay(newOverlay);
          }
        }
      } catch (error) {
        console.error("Error processing files:", error);
      }
    },
    [overlays, getAspectRatioDimensions, addOverlay, currentFPS]
  );

  const handleOverlayChange = (updatedOverlay: Overlay) => {
    changeOverlay(updatedOverlay.id, () => updatedOverlay);
  };

  const { width: compositionWidth, height: compositionHeight } =
    getAspectRatioDimensions();

  const handleTimelineClick = useCallback(
    (frameOrEvent: number | React.MouseEvent<HTMLDivElement>) => {
      if (isPlaying) {
        return; // Don't seek while playing
      }

      let targetFrame: number;

      if (typeof frameOrEvent === "number") {
        targetFrame = frameOrEvent;
      } else {
        const e = frameOrEvent;
        e.preventDefault();
        e.stopPropagation();

        const timelineRect = e.currentTarget.getBoundingClientRect();
        const clickX = e.clientX - timelineRect.left;
        const clickPercentage = Math.max(
          0,
          Math.min(1, clickX / timelineRect.width)
        );
        targetFrame = Math.round(clickPercentage * durationInFrames);
      }

      const clampedFrame = Math.max(0, Math.min(durationInFrames, targetFrame));

      // Use the seekTo function from useVideoPlayer
      seekTo(clampedFrame);
    },
    [isPlaying, seekTo, durationInFrames]
  );

  const inputProps = {
    overlays,
    durationInFrames,
    fps: FPS,
    width: compositionWidth,
    height: compositionHeight,
    src: "",
  };

  const { renderMedia, state } = useRendering(
    "TestComponent",
    inputProps,
    RENDER_TYPE
  );

  const { undo, redo, canUndo, canRedo } = useHistory(overlays, setOverlays);

  useEffect(() => {
    const handleFrameUpdate = (event: CustomEvent) => {
      const { frame, dragEnd } = event.detail;

      setLocalCurrentFrame(frame);

      if (dragEnd && !isPlaying && playerRef.current) {
        playerRef.current.seekTo(frame);
      }
    };

    window.addEventListener(
      "timeline-frame-update",
      handleFrameUpdate as EventListener
    );

    return () => {
      window.removeEventListener(
        "timeline-frame-update",
        handleFrameUpdate as EventListener
      );
    };
  }, [isPlaying, playerRef]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement
      ) {
        return;
      }

      if ((e.ctrlKey || e.metaKey) && e.key === "s") {
        e.preventDefault();
        // handleManualSave();
      }

      if ((e.ctrlKey || e.metaKey) && e.key === "z" && !e.shiftKey) {
        e.preventDefault();
        undo();
      }

      if (
        (e.ctrlKey || e.metaKey) &&
        (e.key === "y" || (e.key === "z" && e.shiftKey))
      ) {
        e.preventDefault();
        redo();
      }

      if (e.code === "Space" && overlays.length > 0) {
        e.preventDefault();
        togglePlayPause();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [undo, redo, togglePlayPause, overlays.length]);

  // Handle XML import split events
  useEffect(() => {
    const handleXmlSplit = (event: CustomEvent) => {
      const { framePosition, timeInSeconds, index, total, xmlFps } =
        event.detail;

      // Convert frame position using the effective FPS of the current overlays
      const effectiveFPS = getEffectiveFPS(overlays);
      // Use time-based conversion to preserve exact timing instead of frame-based
      const adjustedFramePosition =
        xmlFps && xmlFps !== effectiveFPS
          ? Math.round(timeInSeconds * effectiveFPS)
          : framePosition;

      if (xmlFps && xmlFps !== effectiveFPS) {
      } else {
      }

      // Find the overlay that contains this frame position
      // Important: Use current overlays state, not the initial state
      const currentOverlays = overlays; // This should be the most up-to-date overlays
      const overlayToSplit = currentOverlays.find(
        (overlay) =>
          overlay.from <= adjustedFramePosition &&
          overlay.from + overlay.durationInFrames > adjustedFramePosition
      );

      if (overlayToSplit) {
        splitOverlay(overlayToSplit.id, adjustedFramePosition);
      } else {
      }
    };

    const handleXmlSplitsCompleted = (event: CustomEvent) => {
      const { totalCuts, segments, xmlFps } = event.detail;
      const effectiveFPS = getEffectiveFPS(overlays);

      const successfulSplits = overlays.filter((overlay) =>
        event.detail.cutPoints.some((cutPoint: number) => {
          const framePos = xmlFps
            ? Math.round(cutPoint * effectiveFPS)
            : Math.round(cutPoint * 30);
          return (
            overlay.from <= framePos &&
            overlay.from + overlay.durationInFrames > framePos
          );
        })
      ).length;

      // Log segment metadata information (no longer saves to disk)
      if (overlays.length > 0) {
        try {
          // Find the longest overlay to determine video duration
          const longestOverlay = overlays.reduce((longest, current) => {
            const currentEnd = current.from + current.durationInFrames;
            const longestEnd = longest.from + longest.durationInFrames;
            return currentEnd > longestEnd ? current : longest;
          });

          const videoDurationInSeconds =
            (longestOverlay.from + longestOverlay.durationInFrames) /
            effectiveFPS;

          // Format video duration in Final Cut Pro format HH:MM:SS:FF
          const formatTimeWithFrames = (
            timeInSeconds: number,
            fps: number
          ): string => {
            const cleanTimeInSeconds = Math.round(timeInSeconds * 1000) / 1000;
            const totalFrames = Math.round(cleanTimeInSeconds * fps);
            const hours = Math.floor(totalFrames / (3600 * fps));
            const minutes = Math.floor(
              (totalFrames % (3600 * fps)) / (60 * fps)
            );
            const seconds = Math.floor((totalFrames % (60 * fps)) / fps);
            const frames = Math.round(totalFrames % fps);
            return `${hours.toString().padStart(2, "0")}:${minutes
              .toString()
              .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}:${frames
              .toString()
              .padStart(2, "0")}`;
          };

          const videoDurationFormatted = formatTimeWithFrames(
            videoDurationInSeconds,
            xmlFps || effectiveFPS
          );
        } catch (error) {}
      }
    };

    window.addEventListener(
      "xml-split-overlay",
      handleXmlSplit as EventListener
    );
    window.addEventListener(
      "xml-splits-completed",
      handleXmlSplitsCompleted as EventListener
    );

    return () => {
      window.removeEventListener(
        "xml-split-overlay",
        handleXmlSplit as EventListener
      );
      window.removeEventListener(
        "xml-splits-completed",
        handleXmlSplitsCompleted as EventListener
      );
    };
  }, [overlays, splitOverlay]);

  const editorContextValue = useMemo(
    () => ({
      // Core overlay management
      overlays,
      setOverlays,
      selectedOverlayId,
      setSelectedOverlayId,
      changeOverlay,
      handleOverlayChange,
      addOverlay,
      deleteOverlay,
      duplicateOverlay,
      splitOverlay,
      resetOverlays,
      deleteOverlaysByRow,
      updateOverlayStyles,

      // Timeline and current frame
      currentFrame: localCurrentFrame,
      setCurrentFrame: setLocalCurrentFrame,

      // Player state
      isPlaying,
      playerRef,
      togglePlayPause,
      handleTimelineClick,
      playbackRate,
      setPlaybackRate,

      aspectRatio,
      setAspectRatio,
      playerDimensions,
      updatePlayerDimensions,
      getAspectRatioDimensions,
      scaleFactor,
      updateScaleFactor,
      getScaleFactor,
      durationInFrames,
      durationInSeconds,

      renderType: RENDER_TYPE,
      renderMedia,
      state,

      undo,
      redo,
      canUndo,
      canRedo,

      saveProject: () => {},

      handleFilesAdded,
      isProcessing,
      processingProgress,

      currentFPS,
      projectFPS: currentFPS,
      setProjectFPS: () => {},
      fps: currentFPS,
      setCurrentFPS: () => {},
      // ADD THIS MISSING PROPERTY:
      deleteVideo: (index: number) => {
        // Implementation for deleting video by index
        // You can use deleteOverlay or implement custom logic
        const videoOverlays = overlays.filter(
          (o) => o.type === OverlayType.VIDEO
        );
        if (videoOverlays[index]) {
          deleteOverlay(videoOverlays[index].id);
        }
      },
      formatTime: (frames: number) => {
        const effectiveFPS =
          overlays.length > 0 ? getEffectiveFPS(overlays) : FPS;

        const totalSeconds = Math.floor(frames / effectiveFPS);
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;

        const frameInSecond = frames - totalSeconds * effectiveFPS;

        // Use actual FPS for frame display instead of hardcoded 30
        const displayFrame = Math.floor(frameInSecond);

        // Dynamic padding based on FPS (2 digits for FPS < 100, 3 for higher)
        const framePadding = effectiveFPS < 100 ? 2 : 3;

        return `${hours.toString().padStart(2, "0")}:${minutes
          .toString()
          .padStart(2, "0")}:${seconds
          .toString()
          .padStart(2, "0")}.${displayFrame
          .toString()
          .padStart(framePadding, "0")}`;
      },
    }),
    [
      localCurrentFrame,
      setLocalCurrentFrame,
      overlays,
      setOverlays,
      selectedOverlayId,
      setSelectedOverlayId,
      changeOverlay,
      handleOverlayChange,
      addOverlay,
      deleteOverlay,
      duplicateOverlay,
      splitOverlay,
      resetOverlays,
      deleteOverlaysByRow,
      updateOverlayStyles,
      isPlaying,
      playerRef,
      togglePlayPause,
      handleTimelineClick,
      playbackRate,
      setPlaybackRate,
      aspectRatio,
      setAspectRatio,
      playerDimensions,
      updatePlayerDimensions,
      getAspectRatioDimensions,
      scaleFactor,
      updateScaleFactor,
      getScaleFactor,
      durationInFrames,
      durationInSeconds,
      renderMedia,
      state,
      undo,
      redo,
      canUndo,
      canRedo,
      handleFilesAdded,
      isProcessing,
      processingProgress,
      currentFPS,
    ]
  );
  return (
    <AppProvider>
      <UISidebarProvider>
        <EditorSidebarProvider>
          <KeyframeProvider>
            <TimelineProvider>
              <EditorProvider value={editorContextValue}>
                <LocalMediaProvider>
                  <AssetLoadingProvider>
                    <SidebarInset>
                      <Editor />
                      {/* <MinimalVideoTest /> */}
                    </SidebarInset>

                    {isProcessing && (
                      <div className="fixed bottom-4 left-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-lg z-50">
                        <div className="flex items-center gap-3">
                          <div className="animate-spin rounded-full h-5 w-5 border-2 border-blue-500 border-t-transparent" />
                          <div className="flex flex-col gap-1">
                            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              Processing files...
                            </span>
                            <div className="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                              <div
                                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                                style={{
                                  width: `${Math.round(processingProgress)}%`,
                                }}
                              />
                            </div>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {Math.round(processingProgress)}% complete
                            </span>
                          </div>
                        </div>
                      </div>
                    )}

                    {overlays.length === 0 && !isProcessing && (
                      <div className="fixed bottom-4 right-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 shadow-lg z-40">
                        <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
                          <span className="text-2xl">💡</span>
                          <div className="text-sm">
                            <div className="font-medium">Ready to start!</div>
                            <div className="text-blue-600 dark:text-blue-400">
                              Drag files to the video player to begin
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </AssetLoadingProvider>
                </LocalMediaProvider>
              </EditorProvider>
            </TimelineProvider>
          </KeyframeProvider>
        </EditorSidebarProvider>
      </UISidebarProvider>
      <ImportMarkersModal />
      <ExportModal />
      <ExportMarkersModal />
    </AppProvider>
  );
}
