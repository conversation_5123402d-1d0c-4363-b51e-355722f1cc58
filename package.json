{"name": "PLANCK", "version": "0.1.0", "private": true, "main": "electron/main.js", "scripts": {"dev": "next dev", "build": "next build && next export", "start": "next start", "electron": "concurrently \"next dev\" \"wait-on http://localhost:3000 && electron .\"", "electron:build": "next build && next export && electron-builder", "electron:pack": "electron-builder --dir", "electron:dist": "electron-builder", "lint": "next lint", "deploy": "node deploy.mjs", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/material": "^7.2.0", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@remotion/bundler": "v4.0.272", "@remotion/captions": "4.0.272", "@remotion/cli": "4.0.272", "@remotion/cloudrun": "v4.0.272", "@remotion/google-fonts": "4.0.272", "@remotion/lambda": "4.0.272", "@remotion/player": "v4.0.272", "@remotion/renderer": "v4.0.272", "@remotion/studio": "v4.0.272", "@wavesurfer/react": "^1.0.11", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.5.6", "gray-matter": "^4.0.3", "jszip": "^3.10.1", "lodash": "^4.17.21", "lucide-react": "^0.438.0", "next": "^14.2.20", "next-themes": "^0.4.4", "react": "^18.3.1", "react-best-gradient-color-picker": "^3.0.14", "react-dom": "^18", "react-hotkeys-hook": "^4.6.1", "remotion": "4.0.272", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uuid": "^10.0.0", "wavesurfer.js": "^7.10.1", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/config-array": "^0.19.1", "@eslint/object-schema": "^2.1.5", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.12", "@types/node": "^20", "@types/react": "^18.3.18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "babel-jest": "^29.7.0", "concurrently": "^9.2.0", "electron": "^37.2.0", "electron-builder": "^26.0.12", "eslint": "^8.57.1", "eslint-config-next": "^14.2.8", "glob": "^11.0.1", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lru-cache": "^11.0.2", "postcss": "^8", "rimraf": "^6.0.1", "tailwindcss": "^3.4.1", "typescript": "^5", "wait-on": "^8.0.3"}, "build": {"appId": "com.mochachai.app", "productName": "ＰＬＡＮＣＫ", "files": ["build/**/*", "main/**/*", "out/**/*", "electron/**/*", "electron/python-scripts/deployment/dist/**/*", "!**/node_modules/**/*"], "directories": {"buildResources": "assets"}, "extraResources": [{"from": "electron/python-scripts/deployment/dist", "to": "python-scripts", "filter": ["**/*"]}]}}