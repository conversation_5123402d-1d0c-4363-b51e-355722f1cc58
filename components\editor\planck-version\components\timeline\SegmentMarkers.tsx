import React, { useEffect, useRef } from "react";
import { useCutStore } from "@/store/cutStore";
import { getEffectiveFPS } from "../../utils/fps-utils";

interface SegmentMarkersProps {
  durationInFrames: number;
  fps: number;
  currentFrame: number;
  overlays?: any[];
}

const SegmentMarkers: React.FC<SegmentMarkersProps> = ({
  durationInFrames,
  fps,
  currentFrame,
  overlays = [],
}) => {
  const { cutTimes, clearCutTimes } = useCutStore();
  const overlaysRef = useRef(overlays);

  // Calculate effective FPS (same logic as timeline-controls)
  const effectiveFPS = overlays.length > 0 ? getEffectiveFPS(overlays) : fps;

  // Effect to listen for undo/redo events
  useEffect(() => {
    const handleUndoRedo = () => {
      console.log('Undo/Redo detected - refreshing segment markers');
      // Force re-render of markers by clearing and immediately recalculating
      // This ensures markers stay in sync with any undo/redo operations
    };

    // Listen for custom undo/redo events if they exist
    window.addEventListener('editor-undo', handleUndoRedo);
    window.addEventListener('editor-redo', handleUndoRedo);

    return () => {
      window.removeEventListener('editor-undo', handleUndoRedo);
      window.removeEventListener('editor-redo', handleUndoRedo);
    };
  }, []);

  // Convert CutTime to frame number
  const cutTimeToFrame = (cutTime: any) => {
    const totalSeconds = cutTime.hours * 3600 + cutTime.minutes * 60 + cutTime.seconds;
    const totalFrames = totalSeconds * effectiveFPS + cutTime.frames;
    return Math.round(totalFrames);
  };

  // Convert cut times to frame numbers and sort them
  const cutFrames = cutTimes
    .map(cutTime => cutTimeToFrame(cutTime))
    .sort((a, b) => a - b);

  // Create adjusted frames for marker positioning (-1 frame offset)
  const markerFrames = cutFrames.map(frame => Math.max(0, frame - 1));

  // Debug logging
  console.log('SegmentMarkers Debug:', {
    cutTimes: cutTimes.map(ct => `${ct.hours}:${ct.minutes}:${ct.seconds}.${ct.frames}`),
    cutFrames,
    markerFrames,
    durationInFrames,
    fps,
    effectiveFPS,
    overlaysCount: overlays.length,
    currentFrame
  });

  // Determine which segment the current frame is in
  let currentSegmentIndex = 0;
  for (let i = 0; i < cutFrames.length; i++) {
    if (currentFrame >= cutFrames[i]) {
      currentSegmentIndex = i + 1;
    } else {
      break;
    }
  }

  // Total number of segments
  const totalSegments = cutFrames.length + 1;
  const isFirstSegment = currentSegmentIndex === 0;
  const isLastSegment = currentSegmentIndex === totalSegments - 1;

  return (
    <>
      {/* First Segment Start Marker - only show if there are cuts, positioned at first cut -1 frame */}
      {cutFrames.length > 0 && (
        <div
          className="absolute top-0 bottom-0 flex flex-col items-start pointer-events-none z-30"
          style={{
            left: `${(markerFrames[0] / durationInFrames) * 100}%`,
            transform: "translateX(2px)",
          }}
        >
          <div className={`w-1 h-full shadow-sm ${
            isFirstSegment ? 'bg-green-500' : 'bg-green-300 dark:bg-green-700'
          }`} />
          <div className={`absolute top-0 left-1 text-xs font-mono font-bold px-2 py-1 rounded whitespace-nowrap select-none border ${
            isFirstSegment 
              ? 'text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900/80 border-green-400 dark:border-green-500' 
              : 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/40 border-green-300 dark:border-green-600'
          }`}>
            FIRST
          </div>
        </div>
      )}

      {/* When no cuts exist, show start marker at frame 0 */}
      {cutFrames.length === 0 && (
        <div
          className="absolute top-0 bottom-0 flex flex-col items-start pointer-events-none z-30"
          style={{
            left: "0%",
            transform: "translateX(2px)",
          }}
        >
          <div className={`w-1 h-full shadow-sm ${
            isFirstSegment ? 'bg-green-500' : 'bg-green-300 dark:bg-green-700'
          }`} />
          <div className={`absolute top-0 left-1 text-xs font-mono font-bold px-2 py-1 rounded whitespace-nowrap select-none border ${
            isFirstSegment 
              ? 'text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900/80 border-green-400 dark:border-green-500' 
              : 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/40 border-green-300 dark:border-green-600'
          }`}>
            FIRST
          </div>
        </div>
      )}

      {/* Show markers for all cut points */}
      {markerFrames.map((markerFrame, index) => {
        const isFirstCut = index === 0;
        const isLastCut = index === markerFrames.length - 1;
        
        return (
          <div
            key={`cut-marker-${index}`}
            className="absolute top-0 bottom-0 flex flex-col items-center pointer-events-none z-30"
            style={{
              left: `${(markerFrame / durationInFrames) * 100}%`,
              transform: "translateX(-50%)",
            }}
          >
            <div className={`w-1 h-full shadow-sm ${
              isFirstCut ? 'bg-green-500' : 'bg-blue-500'
            }`} />
            {isLastCut && (
              <div className={`absolute top-0 left-2 text-xs font-mono font-bold px-2 py-1 rounded whitespace-nowrap select-none border ${
                isLastSegment
                  ? 'text-orange-700 dark:text-orange-300 bg-orange-100 dark:bg-orange-900/80 border-orange-400 dark:border-orange-500'
                  : 'text-orange-600 dark:text-orange-400 bg-orange-50 dark:bg-orange-900/40 border-orange-300 dark:border-orange-600'
              }`}>
                LAST
              </div>
            )}
          </div>
        );
      })}

      {/* Last Segment End Marker (at end of timeline -1 frame) */}
      {cutFrames.length > 0 && (
        <div
          className="absolute top-0 bottom-0 flex flex-col items-end pointer-events-none z-30"
          style={{
            left: `${((durationInFrames - 1) / durationInFrames) * 100}%`,
            transform: "translateX(-50%)",
          }}
        >
          <div className={`w-1 h-full shadow-sm ${
            isLastSegment ? 'bg-orange-500' : 'bg-orange-300 dark:bg-orange-700'
          }`} />
        </div>
      )}

      {/* When no cuts exist, show end marker for the single segment (-1 frame from end) */}
      {cutFrames.length === 0 && (
        <div
          className="absolute top-0 bottom-0 flex flex-col items-end pointer-events-none z-30"
          style={{
            left: `${((durationInFrames - 1) / durationInFrames) * 100}%`,
            transform: "translateX(-50%)",
          }}
        >
          <div className="w-1 h-full bg-green-500 shadow-sm" />
          <div className="absolute top-0 right-1 text-xs text-green-700 dark:text-green-300 font-mono font-bold bg-green-100 dark:bg-green-900/80 px-2 py-1 rounded whitespace-nowrap select-none border border-green-400 dark:border-green-500">
            END
          </div>
        </div>
      )}
    </>
  );
};

export default SegmentMarkers;