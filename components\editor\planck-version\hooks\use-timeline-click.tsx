import { useCallback } from "react";

type PlayerRef = React.RefObject<{
  seekTo: (time: number) => void;
  pause: () => void;
  getCurrentFrame: () => number;
}>;

/**
 * A custom hook that creates a click handler for timeline/progress bar interactions.
 * When clicked, it calculates the relative position and seeks the player to that timestamp.
 */
export const useTimelineClick = (
  playerRef: PlayerRef,
  durationInFrames: number,
  fps: number = 30,
  isPlaying: boolean = false
) => {
  return useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();

      console.log("🖱️ TIMELINE CLICK DEBUG:");
      console.log("  - isPlaying:", isPlaying);
      console.log("  - durationInFrames:", durationInFrames);
      console.log("  - fps:", fps);

      // Don't seek while playing to avoid conflicts
      if (isPlaying) {
        console.log("  - ⏸️ Ignoring click - video is playing");
        return;
      }

      // Get current frame before seeking
      const currentFrameBeforeSeek = playerRef.current
        ? Math.round(playerRef.current.getCurrentFrame())
        : 0;

      console.log("  - currentFrameBeforeSeek:", currentFrameBeforeSeek);

      const timelineRect = e.currentTarget.getBoundingClientRect();
      const clickX = e.clientX - timelineRect.left;
      const clickPercentage = Math.max(
        0,
        Math.min(1, clickX / timelineRect.width)
      );

      // Calculate new frame position
      const newFrame = Math.round(clickPercentage * durationInFrames);
      const clampedFrame = Math.max(0, Math.min(durationInFrames, newFrame));
      const timeInSeconds = clampedFrame / fps;

      console.log("  - clickX:", clickX);
      console.log("  - timelineWidth:", timelineRect.width);
      console.log("  - clickPercentage:", clickPercentage);
      console.log("  - newFrame:", newFrame);
      console.log("  - clampedFrame:", clampedFrame);
      console.log("  - timeInSeconds:", timeInSeconds);

      // Update video player first
      if (playerRef.current) {
        console.log("  - 🎯 Calling seekTo with:", timeInSeconds);
        playerRef.current.seekTo(timeInSeconds);

        // Small delay to ensure the seek operation completes
        setTimeout(() => {
          console.log("  - 📡 Dispatching timeline-frame-update after seek");

          // Verify the seek worked
          if (playerRef.current) {
            const actualFrameAfterSeek = Math.round(
              playerRef.current.getCurrentFrame()
            );
            console.log("  - actualFrameAfterSeek:", actualFrameAfterSeek);
            console.log(
              "  - seekDifference:",
              Math.abs(actualFrameAfterSeek - clampedFrame)
            );
          }

          // Dispatch event for timeline UI updates after seeking
          window.dispatchEvent(
            new CustomEvent("timeline-frame-update", {
              detail: {
                frame: clampedFrame,
                isDragging: false,
                seekComplete: true,
              },
            })
          );
        }, 50);
      } else {
        console.log("  - ❌ PlayerRef is null!");
      }
    },
    [playerRef, durationInFrames, fps, isPlaying]
  );
};
