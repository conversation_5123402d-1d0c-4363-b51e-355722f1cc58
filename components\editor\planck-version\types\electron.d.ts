interface ElectronAPI {
    // Window controls
    minimizeWindow: () => void;
    maximizeWindow: () => void;
    closeWindow: () => void;

    // Project operations
    saveVideo: () => void;
    openProject: () => void;

    // Python analysis
    runPythonAnalysis: (requestData: any) => Promise<{
        success: boolean;
        data?: any;
        error?: string;
    }>;

    // File operations
    onMenuSave: (buffer: Buffer) => Promise<{
        success: boolean;
        filePath?: string;
        metaPath?: string;
        message?: string;
        error?: string;
        videoFileName?: string;
    }>;

    onSaveVideo: (
        buffer?: Buffer | undefined,
        metadata?: any,
        aiVideoAnalytics?: any
    ) => Promise<{
        success: boolean;
        filePath?: string;
        metaPath?: string;
        message?: string;
        error?: string;
        videoFileName?: string;
    }>;

    getLastSavedMetadata: () => Promise<any>;

    // Auto cut functionality
    selectVideoFile: () => Promise<{
        success: boolean;
        filePath?: string;
        error?: string;
    }>;
    videoAutoCut: (config: any) => Promise<{
        success: boolean;
        data?: any;
        error?: string;
    }>;

    // File system operations
    readFileBuffer: (path: string) => Promise<ArrayBuffer>;
    writeFile: (path: string, data: string) => Promise<void>;
    fileExists: (path: string) => Promise<boolean>;
    readFile: (path: string, encoding?: string) => Promise<string>;

    // Additional methods that might be used
    maximize?: () => void; // Make it optional in case it's not always available
    minimize?: () => void;
    closeWindow?: () => void;
}

interface Window {
    electronAPI: ElectronAPI;
}

declare global {
    interface Window {
        electronAPI: ElectronAPI;
    }
}

export { }; // This makes the file a module