import {
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
} from "@mui/material";
import { CheckedIcon, UncheckedIcon } from "../icon/CustomIcon";
import CustomModal from "../CustomModal";
import Button from "../Button";
import { useExportModal } from "@/store/modalExportStore";

const ExportModal = () => {
  const { modalExport, closeExportModal } = useExportModal();
  const handleExportModal = () => {
    closeExportModal();
  };
  return (
    <CustomModal
      open={modalExport}
      onClose={() => {
        handleExportModal();
      }}
      title="Export media as.."
    >
      <FormControl>
        <RadioGroup
          className="gap-4"
          name="col-radio-buttons-group"
          defaultValue={"aaf"}
        >
          <FormControlLabel
            value="aaf"
            control={
              <Radio icon={<UncheckedIcon />} checkedIcon={<CheckedIcon />} />
            }
            label="AAF"
            sx={{
              "& .MuiFormControlLabel-label": {
                fontSize: "14px",
                fontFamily: "Inter, sans-serif",
                color: "white",
              },
            }}
          />
          <FormControlLabel
            value="wav"
            control={
              <Radio icon={<UncheckedIcon />} checkedIcon={<CheckedIcon />} />
            }
            label="WAV"
            sx={{
              "& .MuiFormControlLabel-label": {
                fontSize: "14px",
                fontFamily: "Inter, sans-serif",
                color: "white",
              },
            }}
          />
        </RadioGroup>
        <div className="flex gap-4 mt-8 items-center w-full">
          <Button
            customClass={"flex-grow shrink-0 text-white font-inter"}
            variant="danger"
            onClick={() => handleExportModal()}
          >
            Cancel
          </Button>
          <Button customClass={"flex-grow shrink-0 text-white font-inter"}>
            Import
          </Button>
        </div>
      </FormControl>
    </CustomModal>
  );
};

export default ExportModal;
