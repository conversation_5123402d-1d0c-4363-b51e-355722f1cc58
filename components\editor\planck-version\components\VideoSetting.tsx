/* eslint-disable react-hooks/exhaustive-deps */
import {
  Box,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  Slider,
  TextField,
  SelectChangeEvent,
} from "@mui/material";
import { useCallback, useEffect, useState } from "react";
import SelectEnv from "./environtment-criteria/SelectEnv";
import Button from "./Button";
import ItemSetting from "./ItemSetting";
import PopupSetting from "./PopupSetting";
import { CheckedIcon, UncheckedIcon } from "./icon/CustomIcon"; // or "./icon/CustomIcon"
import {
  AudioTypeItems,
  envItem,
  VideoTypeItems,
  weatherItems,
  GroundTextureItems,
  GroundMaterial_Indoor_Hard_Items,
  GroundMaterial_Indoor_Soft_Items,
  GroundMaterial_Outdoor_Hard_Items,
  GroundMaterial_Outdoor_Soft_Items,
  FootwearItems,
} from "../data/VideoSetting";
import { useAppContext } from "../hooks/useAppContext";
import { useProjectPath } from "../hooks/useProjectPath";
import {
  getLastSavedVideoBuffer,
  saveVideoProject,
} from "../utils/save-project";
import Image from "next/image";
import sparkles from "./icon/sparkles.svg";
import setting from "./icon/setting.svg";
import download from "./icon/download.svg";
import user from "./icon/user.svg";
import help from "./icon/help.svg";
import loading from "./icon/loading.gif";
import { Overlay, OverlayType } from "../../planck-version/types";
import { getEffectiveFPS } from "../../planck-version/utils/fps-utils";
import path from "path";
import { useTimeline } from "../../planck-version/contexts/timeline-context";
import { useEditorContext } from "../../planck-version/contexts/editor-context";
import { useSegmentStore } from "@/store/segmentStore";
import { useAudioDownload } from "../hooks/use-audio-download";
import { useVideoDownload } from "../hooks/use-video-download";
import { useDataVideoStore } from "@/store/dataVideo";
import { showPopupSetting } from "@/store/showPopupSetting";
import { useCutStore } from "@/store/cutStore";
// Define proper TypeScript interfaces
interface SegmentSettings {
  characterSize: string;
  weather: string;
  environment: string;
  shortDescription: string;
  groundTexture: string;
  groundMaterial: string;
  footwear: string;
  fullPrompt: string;
  negativePrompt: string;
  seed: number;
  qualitySounds: number;
  guidenceStrength: number;
}

interface VideoSettingProps {
  videoSrc?: string | null;
  selectedSegment: any | null;
  segments?: any[];
  overlays?: Overlay[];
  durationInFrames?: number;
  currentFrame?: number;
  onSegmentUpdate?: (segment: any) => void;
  onSegmentsChange?: (segments: any[]) => void;
  onFilesAdded?: (files: File[]) => void;
  isProcessing?: boolean;
  onMutedSegmentsChange?: (
    mutedSegments: Array<{
      id: number;
      startFrame: number;
      endFrame: number;
    }>
  ) => void;
  onAddTimelineRow?: () => void;
}

// Define the interface for the Electron API
interface ElectronAPI {
  getLastSavedMetadata(): Promise<any>;
  onSaveVideo(
    buffer: any,
    metadata: object
  ): Promise<{
    success: boolean;
    filePath?: string;
    metaPath?: string;
    message?: string;
    error?: string;
    videoFileName?: string;
    metadata?: {
      saveDir?: string;
      [key: string]: any;
    };
  }>;
  runPythonAnalysis(data: any): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }>;
  runPythonDescription(data: any): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }>;
  readFileBuffer(path: string): Promise<ArrayBuffer>;
  writeFile(path: string, data: string): Promise<void>;
  fileExists(path: string): Promise<boolean>;
  readDirectory(
    path: string,
    options?: { recursive?: boolean }
  ): Promise<string[]>;
  createDirectory(path: string): Promise<void>;
  writeFileBuffer(path: string, data: ArrayBuffer): Promise<void>;
  getDefaultSaveDir(): Promise<string>;
  getUserDownloadsPath(): Promise<string>;

  // Real-time Python analysis progress
  onPythonAnalysisProgress(
    callback: (progressData: {
      type: "data" | "complete" | "error";
      data?: string;
      error?: string;
      timestamp: string;
    }) => void
  ): void;
  removePythonAnalysisProgressListener(): void;
}

// Helper function to format time in HH:MM:SS.MS format
const formatTimeToHHMMSSMS = (timeInSeconds: number): string => {
  const totalSeconds = Math.floor(timeInSeconds);
  const milliseconds = Math.round((timeInSeconds - totalSeconds) * 100); // Convert to centiseconds (2 decimal places)

  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  return `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}.${milliseconds
    .toString()
    .padStart(2, "0")}`;
};

// Helper function to round time to 2 decimal places
const roundTimeToTwoDecimals = (timeInSeconds: number): number => {
  return Math.round(timeInSeconds * 100) / 100;
};

// Safe Electron API access with type checking
const safeElectronAPI: ElectronAPI = {
  getLastSavedMetadata: async (): Promise<any> => {
    if (
      typeof window !== "undefined" &&
      (window as any).electronAPI?.getLastSavedMetadata
    ) {
      return await (window as any).electronAPI.getLastSavedMetadata();
    }
    return null;
  },
  onSaveVideo: async (buffer: any, metadata: object) => {
    if (
      typeof window !== "undefined" &&
      (window as any).electronAPI?.onSaveVideo
    ) {
      return await (window as any).electronAPI.onSaveVideo(buffer, metadata);
    }
    return { success: false, error: "Electron API not available" };
  },
  runPythonAnalysis: async (data: any) => {
    if (
      typeof window !== "undefined" &&
      (window as any).electronAPI?.runPythonAnalysis
    ) {
      return await (window as any).electronAPI.runPythonAnalysis(data);
    }
    return { success: false, error: "Electron API not available" };
  },
  readFileBuffer: async (path: string): Promise<ArrayBuffer> => {
    if (
      typeof window !== "undefined" &&
      (window as any).electronAPI?.readFileBuffer
    ) {
      return await (window as any).electronAPI.readFileBuffer(path);
    }
    throw new Error("Electron API readFileBuffer not available");
  },
  writeFile: async (path: string, data: string): Promise<void> => {
    if (
      typeof window !== "undefined" &&
      (window as any).electronAPI?.writeFile
    ) {
      await (window as any).electronAPI.writeFile(path, data);
      return;
    }
    console.warn("Electron API writeFile not available");
    return;
  },
  fileExists: async (path: string): Promise<boolean> => {
    if (
      typeof window !== "undefined" &&
      (window as any).electronAPI?.fileExists
    ) {
      return await (window as any).electronAPI.fileExists(path);
    }
    return false;
  },
  readDirectory: async (path: string): Promise<string[]> => {
    if (
      typeof window !== "undefined" &&
      (window as any).electronAPI?.readDirectory
    ) {
      return await (window as any).electronAPI.readDirectory(path);
    }
    console.warn("Electron API readDirectory not available");
    return [];
  },
  createDirectory: async (path: string): Promise<void> => {
    if (
      typeof window !== "undefined" &&
      (window as any).electronAPI?.createDirectory
    ) {
      await (window as any).electronAPI.createDirectory(path);
      return;
    }
    console.warn("Electron API createDirectory not available");
    return;
  },
  writeFileBuffer: async (path: string, data: ArrayBuffer): Promise<void> => {
    if (
      typeof window !== "undefined" &&
      (window as any).electronAPI?.writeFileBuffer
    ) {
      await (window as any).electronAPI.writeFileBuffer(path, data);
      return;
    }
    console.warn("Electron API writeFileBuffer not available");
    return;
  },
  getDefaultSaveDir: async (): Promise<string> => {
    if (
      typeof window !== "undefined" &&
      (window as any).electronAPI?.getDefaultSaveDir
    ) {
      return await (window as any).electronAPI.getDefaultSaveDir();
    }
    throw new Error("Electron API getDefaultSaveDir not available");
  },
  getUserDownloadsPath: async (): Promise<string> => {
    if (
      typeof window !== "undefined" &&
      (window as any).electronAPI?.getUserDownloadsPath
    ) {
      return await (window as any).electronAPI.getUserDownloadsPath();
    }
    throw new Error("Electron API getUserDownloadsPath not available");
  },

  // Real-time Python analysis progress
  onPythonAnalysisProgress: (callback) => {
    if (
      typeof window !== "undefined" &&
      (window as any).electronAPI?.onPythonAnalysisProgress
    ) {
      (window as any).electronAPI.onPythonAnalysisProgress(callback);
    }
  },

  removePythonAnalysisProgressListener: () => {
    if (
      typeof window !== "undefined" &&
      (window as any).electronAPI?.removePythonAnalysisProgressListener
    ) {
      (window as any).electronAPI.removePythonAnalysisProgressListener();
    }
  },
  runPythonDescription: async (data: any) => {
    if (
      typeof window !== "undefined" &&
      (window as any).electronAPI?.runPythonDescription
    ) {
      return await (window as any).electronAPI.runPythonDescription(data);
    }
    return { success: false, error: "Electron API not available" };
  },
};

interface ApiAnalyticList {
  character_size: string[];
  environment: Array<{
    name: string;
    subtypes: string[];
  }>;
  footwear: string[];
  weather: string[];
  ground_texture?: string[]; // Optional in case API doesn't provide it
  ground_material?: string[]; // Optional in case API doesn't provide it
  isAnalyseVideo?: boolean;
}

const VideoSetting: React.FC<VideoSettingProps> = ({
  videoSrc,
  selectedSegment,
  overlays = [],
  onMutedSegmentsChange,
  onAddTimelineRow,
}) => {
  const { addOverlay, durationInFrames } = useEditorContext();
  const { visibleRows } = useTimeline();
  const [isHasAudioOverlay, setIsHasAudioOverlay] = useState<boolean>(false);
  const { isAnalyseVideoDone, setIsAnalyseVideoDone } = useAppContext();
  const [mutedSegments, setMutedSegments] = useState<
    Array<{
      id: number;
      startFrame: number;
      endFrame: number;
    }>
  >([]);
  const timeFrameSegment = useSegmentStore((state) => state.timeFrameSegment);
  const { setTimeFrameSegment } = useSegmentStore();
  const { clearCutTimes } = useCutStore();
  const dataSegment = useDataVideoStore((state) => state.segment);
  const isAnalyseVideo = dataSegment?.isAnalyseVideo ?? false;

  useEffect(() => {
    const hasAudio = overlays.some((o) => o.type === "sound");
    setIsHasAudioOverlay(hasAudio);
  }, [overlays]);
  const [apiAnalyticList, setApiAnalyticList] =
    useState<ApiAnalyticList | null>(null);
  const [apiShortDescription, setApiShortDescription] = useState<string>("");

  // State to store video information
  const [processingTime, setProcessingTime] = useState<number>(0);
  const [videoLength, setVideoLength] = useState<number>(0);
  const [videoSize, setVideoSize] = useState<number>(0);

  // State to store API result values temporarily until apiAnalyticList is updated
  const [pendingApiResult, setPendingApiResult] = useState<any>(null);
  const [displayGroundMaterialsList, setDisplayGroundMaterialsList] =
    useState<any>([]);
  // No fallback data - only use API data

  // Helper function to format time in MM:SS format
  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  };

  // Helper function to format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  };

  // Process pending API result when apiAnalyticList is updated
  useEffect(() => {
    if (apiAnalyticList && pendingApiResult) {
      const result = pendingApiResult.result;

      // Set short description
      if (result.short_description) {
        setApiShortDescription(result.short_description);
      }

      if (result.character_size) {
        setCharacterSize(result.character_size);
      }
      if (result.weather) {
        setWeather(result.weather);
      }
      if (result.environment) {
        setEnvironment(result.environment);
      }
      if (result.ground_texture) {
        setGroundTexture(result.ground_texture);
      }
      if (result.ground_material) {
        setGroundMaterial(result.ground_material);
      }
      if (result.footwear) {
        setFootwear(result.footwear);
      }

      // Clear pending result
      setPendingApiResult(null);
    }
  }, [apiAnalyticList, pendingApiResult]);

  // Listen for project load events
  useEffect(() => {
    const handleProjectLoaded = (event: CustomEvent) => {
      if (event.detail) {
        setProjectData(event.detail);

        // Check if project has analyzed segments and load AI analytics
        if (event.detail?.segments?.aiVideoAnalytics) {
          const firstSegment = Object.values(
            event.detail.segments.aiVideoAnalytics
          )[0] as any;
          if (firstSegment && firstSegment.weather) {
            // Create mock AI analytics list for the UI
            const mockAnalyticsList: ApiAnalyticList = {
              character_size: ["small", "medium", "big"],
              weather: ["dry", "wet"],
              footwear: ["barefoot", "shoes", "boots", "sandals"],
              environment: [
                {
                  name: "indoor",
                  subtypes: [
                    "soft",
                    "hard",
                    "carpet",
                    "fabric",
                    "cushion",
                    "tile",
                    "wood",
                    "concrete",
                  ],
                },
                {
                  name: "outdoor",
                  subtypes: [
                    "soft",
                    "hard",
                    "grass",
                    "sand",
                    "dirt",
                    "concrete",
                    "stone",
                    "pavement",
                  ],
                },
              ],
            };

            setApiAnalyticList(mockAnalyticsList);
          }
        }
      } else {
        // If no project data in event, try to load it
        loadProjectData();
      }
    };

    // Listen for custom project loaded events
    // Define the handlers with proper references for cleanup
    const handleNewProjectReset = () => {
      // Clear project data
      setProjectData(null);

      // Reset all analysis-related state
      setIsAnalyse(false);
      setIsAnalyseVideoDone(false);
      setApiAnalyticList(null);
      setApiShortDescription("");
      setPendingApiResult(null);

      // Reset all segment settings to defaults
      setWeather(INITIAL_DEFAULTS.weather);
      setEnvironment(INITIAL_DEFAULTS.environment);
      setGroundTexture(INITIAL_DEFAULTS.groundTexture);
      setGroundMaterial(INITIAL_DEFAULTS.groundMaterial);
      setCharacterSize(INITIAL_DEFAULTS.characterSize);
      setFootwear(INITIAL_DEFAULTS.footwear);
      setFullPrompt(INITIAL_DEFAULTS.fullPrompt);
      setNegativePrompt(INITIAL_DEFAULTS.negativePrompt);
      setSeed(INITIAL_DEFAULTS.seed);
      setQualitySounds(INITIAL_DEFAULTS.qualitySounds);
      setGuidenceStrength(INITIAL_DEFAULTS.guidenceStrength);

      // Reset video information
      setProcessingTime(0);
      setVideoLength(0);
      setVideoSize(0);

      // Reset display lists
      setDisplayGroundMaterialsList([]);

      // Close any open popups
      setShowPopup("");
      setShowNoSegmentMessage(false);

      // Clear data video store segment
      setSegment(null);

      // Clear Zustand stores
      setTimeFrameSegment("");
      clearCutTimes();
    };

    window.addEventListener(
      "project-loaded",
      handleProjectLoaded as EventListener
    );
    window.addEventListener("new-project-reset", handleNewProjectReset);

    return () => {
      window.removeEventListener(
        "project-loaded",
        handleProjectLoaded as EventListener
      );
      window.removeEventListener("new-project-reset", handleNewProjectReset);
    };
  }, []);

  // Watch for selectedSegment changes and load corresponding settings
  useEffect(() => {
    if (
      selectedSegment &&
      selectedSegment.start !== undefined &&
      selectedSegment.end !== undefined
    ) {
      // FIXED: Calculate accurate start/end times from frame positions
      const currentFPS = getEffectiveFPS(overlays) || 30;

      const frameBasedStartTime = roundTimeToTwoDecimals(
        selectedSegment.from / currentFPS
      );
      const frameBasedEndTime = roundTimeToTwoDecimals(
        (selectedSegment.from + selectedSegment.durationInFrames) / currentFPS
      );

      // Use original start/end if they exist and are reasonable, otherwise use frame-based calculation
      const originalStartTime = roundTimeToTwoDecimals(selectedSegment.start);
      const originalEndTime = roundTimeToTwoDecimals(selectedSegment.end);

      // Check if original times are close to frame-based times (within 0.1 seconds tolerance)
      const startTimeDiff = Math.abs(originalStartTime - frameBasedStartTime);
      const endTimeDiff = Math.abs(originalEndTime - frameBasedEndTime);
      const tolerance = 0.1;

      const accurateStartTime =
        startTimeDiff <= tolerance ? originalStartTime : frameBasedStartTime;
      const accurateEndTime =
        endTimeDiff <= tolerance ? originalEndTime : frameBasedEndTime;

      // Use accurate time values for segment matching
      findSegmentSettingsByTime(accurateStartTime, accurateEndTime);
    }
    setShowNoSegmentMessage(false);
  }, [selectedSegment, overlays]);

  // Save video source when it changes
  useEffect(() => {
    if (videoSrc) {
      saveVideoProject(videoSrc);
    }
  }, [videoSrc]);

  // Define initial default values as constants
  const INITIAL_DEFAULTS: SegmentSettings = {
    weather: "dry",
    shortDescription: "",
    environment: "indoor",
    groundTexture: "soft",
    groundMaterial: "carpet",
    characterSize: "big",
    footwear: "barefoot",
    fullPrompt:
      "Enter your custom prompt here or use 'Analyse Video' to generate one automatically...",
    negativePrompt:
      "talking, speech, voice, dialogue, conversation, ambient noise, background noise, room tone, breathing, fabric rustle, music, chatter, murmuring, whispering, echo",
    seed: 0,
    qualitySounds: 50,
    guidenceStrength: 7,
  };

  // All state variables with proper typing
  const [weather, setWeather] = useState<string>(INITIAL_DEFAULTS.weather);
  const [environment, setEnvironment] = useState<string>(
    INITIAL_DEFAULTS.environment
  );
  const [groundTexture, setGroundTexture] = useState<string>(
    INITIAL_DEFAULTS.groundTexture
  );
  const [groundMaterial, setGroundMaterial] = useState<string>(
    INITIAL_DEFAULTS.groundMaterial
  );
  const [videoType, setVideoType] = useState<string>("mp4");
  const [audioType, setAudioType] = useState<string>("wav");
  const [footwear, setFootwear] = useState<string>(INITIAL_DEFAULTS.footwear);
  const [showNoSegmentMessage, setShowNoSegmentMessage] =
    useState<boolean>(false);
  const [seed, setSeed] = useState<number>(INITIAL_DEFAULTS.seed);
  const [qualitySounds, setQualitySounds] = useState<number>(
    INITIAL_DEFAULTS.qualitySounds
  );
  const [guidenceStrength, setGuidenceStrength] = useState<number>(
    INITIAL_DEFAULTS.guidenceStrength
  );
  const { setPage, isLoading, setIsLoading } = useAppContext();
  const [characterSize, setCharacterSize] = useState<string>(
    INITIAL_DEFAULTS.characterSize
  );
  const [fullPrompt, setFullPrompt] = useState<string>(
    INITIAL_DEFAULTS.fullPrompt
  );
  const [negativePrompt, setNegativePrompt] = useState<string>(
    INITIAL_DEFAULTS.negativePrompt
  );

  // Use the audio download hook
  const { projectUrl, pathRef } = useProjectPath();
  const {
    isDownloadingAudio,
    downloadMessage,
    setDownloadMessage,
    downloadAudioFromSecondRow,
  } = useAudioDownload({
    overlays,
    selectedSegment,
    audioType,
    durationInFrames,
    projectUrl,
    pathRef,
  });
  const { showPopup, setShowPopup, isAnalyse, setIsAnalyse } =
    showPopupSetting();
  // Use the video download hook
  const {
    isDownloadingVideo,
    downloadMessage: videoDownloadMessage,
    setDownloadMessage: setVideoDownloadMessage,
    downloadVideoWithAudio,
  } = useVideoDownload({
    overlays,
    selectedSegment,
    videoType,
    durationInFrames,
    compositionWidth: 1280,
    compositionHeight: 720,
    projectUrl,
    pathRef,
  });

  // Auto-dismiss success messages after 5 seconds
  useEffect(() => {
    if (downloadMessage.type === "success") {
      const timer = setTimeout(() => {
        setDownloadMessage({ type: null, message: "" });
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [downloadMessage, setDownloadMessage]);

  // Auto-dismiss video download success messages after 5 seconds
  useEffect(() => {
    if (videoDownloadMessage.type === "success") {
      const timer = setTimeout(() => {
        setVideoDownloadMessage({ type: null, message: "" });
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [videoDownloadMessage, setVideoDownloadMessage]);

  // Auto-dismiss no segment message after 5 seconds
  useEffect(() => {
    if (showNoSegmentMessage) {
      const timer = setTimeout(() => {
        setShowNoSegmentMessage(false);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [showNoSegmentMessage]);

  useEffect(() => {
    if (onMutedSegmentsChange) {
      onMutedSegmentsChange(mutedSegments);
    }
  }, [mutedSegments, onMutedSegmentsChange]);

  // Set up Python analysis progress listener
  useEffect(() => {
    // Cleanup listener on unmount
    return () => {
      safeElectronAPI.removePythonAnalysisProgressListener();
    };
  }, []);
  const { updatePath } = useProjectPath();

  // Add state to store project data
  const [projectData, setProjectData] = useState<any>(null);

  // Add state to cache analyzed segments locally
  const [analyzedSegmentsCache, setAnalyzedSegmentsCache] = useState<
    Map<number, any>
  >(new Map());

  // Immediate debug - check environment

  // Load project data on component mount
  useEffect(() => {
    const loadProjectData = async () => {
      try {
        if (window.electronAPI?.getLastSavedMetadata) {
          const metadata = await window.electronAPI.getLastSavedMetadata();
          setProjectData(metadata);
        }
      } catch (error) {
        // Error loading project metadata
      }
    };

    loadProjectData();
  }, []);

  // Helper function to check if current selected segment has been analyzed
  const isCurrentSegmentAnalyzed = useCallback(() => {
    if (!selectedSegment) return false;

    // Check condition 0: segment exists in local cache (highest priority)
    const condition0 = analyzedSegmentsCache.has(selectedSegment.id);

    // Check condition 1: dataSegment matches current segment and is analyzed
    const condition1 = !!(
      dataSegment?.isAnalyseVideo === true &&
      selectedSegment.id === dataSegment?.segmentId
    );

    // Check condition 2: segment exists in saved project analytics
    const condition2 = !!(
      projectData?.segments?.aiVideoAnalytics &&
      Object.values(projectData.segments.aiVideoAnalytics).some((seg: any) => {
        const isMatch =
          seg.segmentId === selectedSegment.id && seg.isAnalyseVideo === true;
        return isMatch;
      })
    );

    // Check condition 3: Fallback for when dataSegment.isAnalyseVideo is undefined but analysis was done
    // Only apply if dataSegment actually corresponds to the selected segment AND has analysis data
    const condition3 = !!(
      isAnalyseVideoDone &&
      selectedSegment.type === "video" &&
      dataSegment?.segmentId === selectedSegment.id &&
      (dataSegment?.weather || dataSegment?.environment)
    ); // Must have actual analysis data

    const result = condition0 || condition1 || condition2 || condition3;

    return result;
  }, [
    selectedSegment,
    dataSegment,
    projectData,
    isAnalyseVideoDone,
    analyzedSegmentsCache,
  ]);

  useEffect(() => {
    if (environment === "indoor" && groundTexture === "soft") {
      setDisplayGroundMaterialsList(GroundMaterial_Indoor_Soft_Items);
    } else if (environment === "indoor" && groundTexture === "hard") {
      setDisplayGroundMaterialsList(GroundMaterial_Indoor_Hard_Items);
    } else if (environment === "outdoor" && groundTexture === "soft") {
      setDisplayGroundMaterialsList(GroundMaterial_Outdoor_Soft_Items);
    } else {
      setDisplayGroundMaterialsList(GroundMaterial_Outdoor_Hard_Items);
    }
  }, [environment, groundTexture]);
  // Function to load project data when a project is opened
  const loadProjectData = useCallback(async () => {
    // Try multiple approaches to get project data
    if (
      typeof window !== "undefined" &&
      (window as any).electronAPI?.getLastSavedMetadata
    ) {
      try {
        const metadata = await (
          window as any
        ).electronAPI.getLastSavedMetadata();
        if (metadata?.segments?.aiVideoAnalytics) {
          setProjectData(metadata);

          // Load AI analytics data if available
          const firstSegment = Object.values(
            metadata.segments.aiVideoAnalytics
          )[0] as any;
          if (firstSegment && firstSegment.weather) {
            // Create mock AI analytics list for the UI
            const mockAnalyticsList: ApiAnalyticList = {
              character_size: ["small", "medium", "big"],
              weather: ["dry", "wet"],
              footwear: ["barefoot", "shoes", "boots", "sandals"],
              environment: [
                {
                  name: "indoor",
                  subtypes: [
                    "soft",
                    "hard",
                    "carpet",
                    "fabric",
                    "cushion",
                    "tile",
                    "wood",
                    "concrete",
                  ],
                },
                {
                  name: "outdoor",
                  subtypes: [
                    "soft",
                    "hard",
                    "grass",
                    "sand",
                    "dirt",
                    "concrete",
                    "stone",
                    "pavement",
                  ],
                },
              ],
            };

            setApiAnalyticList(mockAnalyticsList);
          }

          return metadata;
        }
      } catch (error) {}
    }

    // Try reading from project URL
    if (
      projectUrl &&
      typeof window !== "undefined" &&
      (window as any).electronAPI?.readFileBuffer
    ) {
      try {
        const plankFilePath = projectUrl.endsWith(".plank")
          ? projectUrl
          : `${projectUrl}/${projectUrl.split("/").pop()}.plank`;

        const exists = await (window as any).electronAPI.fileExists(
          plankFilePath
        );
        if (exists) {
          const buffer = await (window as any).electronAPI.readFileBuffer(
            plankFilePath
          );
          const text = new TextDecoder().decode(buffer);
          const data = JSON.parse(text);

          if (data?.segments?.aiVideoAnalytics) {
            setProjectData(data);
            return data;
          }
        }
      } catch (error) {}
    }

    return null;
  }, [projectUrl]);

  const setSegment = useDataVideoStore((state) => state.setSegment);
  const processSegmentData = useCallback(
    (savedSegments: any, startTime: number, itemId?: number) => {
      const segmentKeys = Object.keys(savedSegments).filter((key) =>
        key.startsWith("segment")
      );

      const timeStringToSeconds = (
        timeStr: string | number,
        _fps = 30
      ): number => {
        if (typeof timeStr === "number") return timeStr;

        // format HH:MM:SS.xx
        const match = timeStr.match(/(?:(\d+):)?(?:(\d+):)?(\d+)(?:\.(\d+))?/);
        if (!match) return parseFloat(timeStr) || 0;

        const [, h, m, s, ms] = match;
        const hours = parseInt(h || "0", 10);
        const minutes = parseInt(m || "0", 10);
        const seconds = parseInt(s || "0", 10);
        let sub = 0;

        if (ms) {
          // jika panjang ≤ 2 digit → treat decimal
          if (ms.length <= 2) {
            sub = parseFloat("0." + ms);
          } else {
            // treat sebagai milidetik
            sub = parseInt(ms) / Math.pow(10, ms.length);
          }
        }

        return hours * 3600 + minutes * 60 + seconds + sub;
      };

      // Method 1: pakai mapping id
      if (itemId && (window as any).segmentIdMapping) {
        const segmentKey = (window as any).segmentIdMapping.get(itemId);
        if (segmentKey && savedSegments[segmentKey]) {
          return applySegmentData(savedSegments[segmentKey], segmentKey);
        }
      }

      // Method 2: cari berdasarkan startTime saja
      for (const segmentKey of segmentKeys) {
        const segment = savedSegments[segmentKey];
        if (!segment?.startTime) continue;

        const parsedTime = timeStringToSeconds(
          segment.startTime,
          segment.fps || 30
        );
        const diff = Math.abs(parsedTime - startTime);

        if (diff < 1.5) {
          setSegment(segment);
          return applySegmentData(segment, segmentKey);
        }
      }

      return null;

      function applySegmentData(segment: any, _segmentKey: string) {
        if (segment.isAnalyseVideo != null) {
          setIsAnalyseVideoDone(true);
          setIsAnalyse(true);
        }
        if (segment.weather) setWeather(segment.weather);
        if (segment.environment) setEnvironment(segment.environment);
        if (segment.groundTexture) setGroundTexture(segment.groundTexture);
        if (segment.groundMaterial) setGroundMaterial(segment.groundMaterial);
        if (segment.footwear) setFootwear(segment.footwear);
        if (segment.characterSize) setCharacterSize(segment.characterSize);
        if (segment.fullPrompt) setFullPrompt(segment.fullPrompt);
        if (segment.processingTime) setProcessingTime(segment.processingTime);
        if (segment.segmentDurationSeconds)
          setVideoLength(segment.segmentDurationSeconds);
        if (segment.videoSize) setVideoSize(segment.videoSize);
        if (segment.shortDescription)
          setApiShortDescription(segment.shortDescription);

        if (segment.settings) {
          if (segment.settings.negativePrompt) {
            setNegativePrompt(segment.settings.negativePrompt);
          }
          if (typeof segment.settings.seed === "number") {
            setSeed(segment.settings.seed);
          }
          if (typeof segment.settings.qualitySounds === "number") {
            setQualitySounds(segment.settings.qualitySounds);
          }
          if (typeof segment.settings.guidenceStrength === "number") {
            setGuidenceStrength(segment.settings.guidenceStrength);
          }
        }

        return segment;
      }
    },
    []
  );

  // Alternative method to try getting project data from other sources
  const tryAlternativeDataSources = useCallback(
    (startTime: number, endTime: number) => {
      // First, try using stored project data
      if (projectData?.segments?.aiVideoAnalytics) {
        processSegmentData(
          projectData.segments.aiVideoAnalytics,
          startTime,
          endTime
        );
        return;
      }

      // Try to load project data if not already loaded
      loadProjectData().then((data) => {
        if (data?.segments?.aiVideoAnalytics) {
          processSegmentData(
            data.segments.aiVideoAnalytics,
            startTime,
            endTime
          );
        }
      });

      // Try to get project data from project path or other context
      if (
        projectUrl &&
        typeof window !== "undefined" &&
        (window as any).electronAPI?.readFileBuffer
      ) {
        // Try to read the .plank file directly
        const plankFilePath = projectUrl.endsWith(".plank")
          ? projectUrl
          : `${projectUrl}/${projectUrl.split("/").pop()}.plank`;

        // Check if the file exists and read it
        if ((window as any).electronAPI?.fileExists) {
          (window as any).electronAPI
            .fileExists(plankFilePath)
            .then((exists: boolean) => {
              if (exists && (window as any).electronAPI?.readFileBuffer) {
                (window as any).electronAPI
                  .readFileBuffer(plankFilePath)
                  .then((buffer: ArrayBuffer) => {
                    try {
                      const text = new TextDecoder().decode(buffer);
                      const projectData = JSON.parse(text);

                      if (projectData?.segments?.aiVideoAnalytics) {
                        processSegmentData(
                          projectData.segments.aiVideoAnalytics,
                          startTime,
                          endTime
                        );
                      } else {
                      }
                    } catch (parseError) {
                      console.error("❌ Error parsing plank file:", parseError);
                    }
                  })
                  .catch((readError: any) => {
                    console.error("❌ Error reading plank file:", readError);
                  });
              }
            })
            .catch((error: any) => {
              console.error("❌ Error checking plank file existence:", error);
            });
        }
      } else {
      }
    },
    [projectData, projectUrl, loadProjectData, processSegmentData]
  );

  // Function to find segment settings by time range
  const findSegmentSettingsByTime = useCallback(
    (startTime: number, endTime: number) => {
      // First try to get saved metadata from Electron API
      if (
        typeof window !== "undefined" &&
        (window as any).electronAPI?.getLastSavedMetadata
      ) {
        (window as any).electronAPI
          .getLastSavedMetadata()
          .then((metadata: any) => {
            if (metadata?.segments?.aiVideoAnalytics) {
              processSegmentData(
                metadata.segments.aiVideoAnalytics,
                startTime,
                endTime
              );
            } else {
              // Alternative approach: Try to read project data from other sources
              tryAlternativeDataSources(startTime, endTime);
            }
          })
          .catch((error: any) => {
            console.error("❌ Error fetching metadata:", error);
            tryAlternativeDataSources(startTime, endTime);
          });
      } else {
        tryAlternativeDataSources(startTime, endTime);
      }

      return null;
    },
    [processSegmentData, tryAlternativeDataSources]
  );

  const findAudioFileWithRetry = async (
    saveDir: string,
    audioFileName: string,
    maxRetries: number = 10,
    retryDelay: number = 1500
  ): Promise<string | null> => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // FIXED: Use proper path.join() for cross-platform compatibility
        const aiDirectory = path.join(saveDir, "ai");
        const expectedFilePath = path.join(aiDirectory, audioFileName);

        // First, check if the AI directory exists
        try {
          const files = await safeElectronAPI.readDirectory(aiDirectory);

          // Look for our file (exact match or case-insensitive)
          const foundFile = files.find(
            (file) =>
              file === audioFileName ||
              file.toLowerCase() === audioFileName.toLowerCase()
          );

          if (foundFile) {
            // FIXED: Use path.join() instead of manual string concatenation
            const fullPath = path.join(aiDirectory, foundFile);

            // Verify the file exists using the file existence check
            const fileExists = await safeElectronAPI.fileExists(fullPath);
            if (fileExists) {
              return fullPath;
            } else {
              // Return the path anyway since we found it in directory listing
              return fullPath;
            }
          } else {
          }
        } catch (dirError) {
          // Fallback: Check if file exists directly without directory listing
          try {
            const fileExists = await safeElectronAPI.fileExists(
              expectedFilePath
            );
            if (fileExists) {
              return expectedFilePath;
            }
          } catch (fileError) {}
        }

        // Additional fallback: Check main directory (in case AI folder structure is different)
        try {
          const mainDirectoryPath = path.join(saveDir, audioFileName);

          const mainFileExists = await safeElectronAPI.fileExists(
            mainDirectoryPath
          );
          if (mainFileExists) {
            return mainDirectoryPath;
          }
        } catch (mainDirError) {}

        // Final fallback: Search recursively for any .wav files
        try {
          const allFiles = await safeElectronAPI.readDirectory(saveDir, {
            recursive: true,
          });
          const wavFiles = allFiles.filter(
            (file) =>
              file.toLowerCase().endsWith(".wav") &&
              path
                .basename(file)
                .toLowerCase()
                .includes(audioFileName.toLowerCase().replace(".wav", ""))
          );

          if (wavFiles.length > 0) {
            // Return the first matching file
            const matchingFile = wavFiles[0];

            return matchingFile;
          }
        } catch (recursiveError) {}
      } catch (error) {}

      if (attempt < maxRetries) {
        await new Promise((resolve) => setTimeout(resolve, retryDelay));
      }
    }

    return null;
  };

  const createCustomAIAudioOverlay = async (
    audioFile: File,
    selectedSegment: any,
    _foundAudioPath: string
  ) => {
    try {
      const segmentStartFrame = selectedSegment.from;
      const segmentDurationFrames = selectedSegment.durationInFrames;

      let selectedComponentRow = 0;
      if (selectedSegment && overlays.length > 0) {
        const selectedOverlay = overlays.find(
          (overlay) => overlay.id === selectedSegment.id
        );
        if (selectedOverlay) {
          selectedComponentRow = selectedOverlay.row;
        }
      }

      const newAIAudioRow = selectedComponentRow + 1;
      const aiAudioStartFrame = segmentStartFrame;
      const finalDurationFrames = segmentDurationFrames;

      const aiAudioOverlay: any = {
        id: Date.now() + Math.random() * 1000,
        type: OverlayType.SOUND,
        from: aiAudioStartFrame,
        durationInFrames: finalDurationFrames,
        row: newAIAudioRow,
        left: 0,
        top: 0,
        width: 0,
        height: 0,
        isDragging: false,
        rotation: 0,
        src: URL.createObjectURL(audioFile),
        content: audioFile.name,
        startFromSound: 0,
        styles: {
          opacity: 1,
          zIndex: 1,
          volume: 1,
        },
      };

      if (newAIAudioRow >= visibleRows && onAddTimelineRow) {
        const rowsToAdd = newAIAudioRow + 1 - visibleRows;
        for (let i = 0; i < rowsToAdd; i++) {
          onAddTimelineRow();
        }
      }

      if (addOverlay) {
        try {
          addOverlay(aiAudioOverlay as any);
        } catch (error) {
          console.error("❌ Failed to add audio overlay:", error);
        }
      }

      return {
        aiAudioOverlay,
        newRowIndex: newAIAudioRow,
        selectedComponentRow,
        segmentStartFrame,
        finalDurationFrames,
      };
    } catch (error) {
      console.error("❌ Error in createCustomAIAudioOverlay:", error);
      throw error;
    }
  };

  const handleGenerate = async (): Promise<void> => {
    try {
      setIsLoading(true);

      // 🔥 FIXED: Properly check for selectedSegment including ID = 0
      if (
        !selectedSegment ||
        selectedSegment.id === null ||
        selectedSegment.id === undefined
      ) {
        console.error("No segment selected or invalid segment ID");
        setIsLoading(false);
        return;
      }

      // 🔥 STEP 1: ADD the selected segment to muted segments list (for immediate muting)
      setMutedSegments((prev) => {
        const filtered = prev.filter((seg) => seg.id !== selectedSegment.id);
        const newMutedSegments = [
          ...filtered,
          {
            id: selectedSegment.id,
            startFrame: selectedSegment.from,
            endFrame: selectedSegment.from + selectedSegment.durationInFrames,
          },
        ];
        return newMutedSegments;
      });

      // 🔥 STEP 2: Get current settings for AI generation
      const cutTimes = useCutStore.getState().cutTimes;
      const currentSegmentSettings = {
        weather,
        environment,
        groundTexture,
        groundMaterial,
        footwear,
        seed,
        apiShortDescription,
        isAnalyseVideo,
        qualitySounds,
        guidenceStrength,
        fullPrompt,
        processingTime,
        videoSize,
        videoLength,
        negativePrompt,
        timeFrameSegment: cutTimes,
      };

      // 🔥 STEP 3: Check if we have existing project metadata
      const lastMetadata = await safeElectronAPI.getLastSavedMetadata();

      if (!lastMetadata || (!lastMetadata.saveDir && !lastMetadata.savedPath)) {
        console.error(
          "❌ No existing project found. Please save the project first using 'Analyse Video' before generating audio."
        );
        setIsLoading(false);
        return;
      }

      // 🔥 STEP 4: Use existing project data
      const saveDir =
        lastMetadata.saveDir || path.dirname(lastMetadata.savedPath);
      const savedVideoFileName = lastMetadata.video;

      // 🔥 STEP 5: Prepare segment metadata
      const currentFPS = getEffectiveFPS(overlays) || 30;
      const frameBasedStartTime = roundTimeToTwoDecimals(
        selectedSegment.from / currentFPS
      );
      const frameBasedEndTime = roundTimeToTwoDecimals(
        (selectedSegment.from + selectedSegment.durationInFrames) / currentFPS
      );

      const originalStartTime = roundTimeToTwoDecimals(selectedSegment.start);
      const originalEndTime = roundTimeToTwoDecimals(selectedSegment.end);

      const startTimeDiff = Math.abs(originalStartTime - frameBasedStartTime);
      const endTimeDiff = Math.abs(originalEndTime - frameBasedEndTime);
      const tolerance = 0.1;

      const accurateStartTime =
        startTimeDiff <= tolerance ? originalStartTime : frameBasedStartTime;
      const accurateEndTime =
        endTimeDiff <= tolerance ? originalEndTime : frameBasedEndTime;

      const timestamp = new Date().getTime();
      const uniqueId = `${timestamp}_${selectedSegment.id}`;

      const segmentMetadata = {
        segmentId: selectedSegment.id,
        segmentName:
          selectedSegment.name || `Custom Segment ${selectedSegment.id}`,
        startTime: formatTimeToHHMMSSMS(accurateStartTime),
        endTime: formatTimeToHHMMSSMS(accurateEndTime),
        audioFile: "", // Will be updated later
        characterSize: "medium",
        weather: currentSegmentSettings.weather,
        isAnalyseVideo: true,
        processingTime: currentSegmentSettings.processingTime,
        videoSize: currentSegmentSettings.videoSize,
        segmentDurationSeconds: currentSegmentSettings.videoLength,
        shortDescription: currentSegmentSettings.apiShortDescription,
        environment: currentSegmentSettings.environment,
        groundTexture: currentSegmentSettings.groundTexture,
        groundMaterial: currentSegmentSettings.groundMaterial,
        footwear: currentSegmentSettings.footwear,
        fullPrompt: currentSegmentSettings.fullPrompt,
        negativePrompt: currentSegmentSettings.negativePrompt,
        seed: currentSegmentSettings.seed,
        qualitySounds: currentSegmentSettings.qualitySounds,
        guidenceStrength: currentSegmentSettings.guidenceStrength,
        uniqueId,
        timeFrameSegment: currentSegmentSettings.timeFrameSegment,
      };

      // 🔥 STEP 6: Save metadata only (no video buffer) - This won't show save dialog
      let saveResult = await safeElectronAPI.onSaveVideo(null, segmentMetadata);

      if (!saveResult.success) {
        console.error(
          "❌ Failed to save metadata:",
          saveResult.error || saveResult.message
        );
        setIsLoading(false);
        return;
      }

      // 🔥 STEP 7: Prepare request data for Python analysis (AI AUDIO GENERATION)
      const requestData = {
        destinationPath: saveDir,
        mock_mode: false,
        analysis_type: "audio_generation",
        videoFileName: savedVideoFileName,
        request_id: `req_${Date.now()}_${selectedSegment.id}`,
        selectedSegment: {
          id: selectedSegment.id,
          name: selectedSegment.name,
          start: Math.round(accurateStartTime * 1000),
          end: Math.round(accurateEndTime * 1000),
        },
        parameters: {
          weather: currentSegmentSettings.weather,
          environment: currentSegmentSettings.environment,
          ground_texture: currentSegmentSettings.groundTexture,
          ground_material: currentSegmentSettings.groundMaterial,
          footwear: currentSegmentSettings.footwear,
          seed: currentSegmentSettings.seed,
          quality_sounds: currentSegmentSettings.qualitySounds,
          guidance_strength: currentSegmentSettings.guidenceStrength,
          full_prompt: fullPrompt,
          negative_prompt: currentSegmentSettings.negativePrompt,
        },
      };

      // 🔥 STEP 8: Run Python analysis (AI AUDIO GENERATION)
      const pythonResult = await safeElectronAPI.runPythonAnalysis(requestData);

      if (!pythonResult.success) {
        console.error("❌ Python analysis failed:", pythonResult.error);
        setIsLoading(false);
        return;
      }

      // 🔥 STEP 9: Handle the generated audio file
      const generatedAudioFile = pythonResult.data?.generated_file;
      if (!generatedAudioFile) {
        console.error("❌ No audio file name returned from Python analysis");
        setIsLoading(false);
        return;
      }

      const audioFileName = path.basename(generatedAudioFile);

      // 🔥 STEP 10: Find the audio file with retry mechanism
      const audioPath = await findAudioFileWithRetry(
        saveDir,
        audioFileName,
        10,
        1500
      );

      if (!audioPath) {
        console.error(
          `❌ Could not find generated audio file: ${audioFileName}`
        );
        setIsLoading(false);
        return;
      }

      // 🔥 STEP 11: Update metadata with actual audio filename (metadata-only save)
      const updatedSegmentMetadata = {
        ...segmentMetadata,
        audioFile: audioFileName,
      };

      // Save final metadata update with actual audio file name (metadata-only)
      await safeElectronAPI.onSaveVideo(null, updatedSegmentMetadata);

      // 🔥 STEP 12: Create and place AI audio overlay
      try {
        // Read the audio file as buffer using Electron API
        const audioBuffer = await safeElectronAPI.readFileBuffer(audioPath);

        // Create a Blob from the buffer
        const audioBlob = new Blob([audioBuffer], {
          type: "audio/wav",
        });

        // Create a File object from the blob with AI prefix
        const audioFileForPlacement = new File(
          [audioBlob],
          `AI_${audioFileName}`,
          {
            type: "audio/wav",
            lastModified: Date.now(),
          }
        );

        // 🔥 Create and place the AI audio overlay
        await createCustomAIAudioOverlay(
          audioFileForPlacement,
          selectedSegment,
          audioPath
        );
      } catch (fileReadError) {
        console.error(
          "❌ Error reading audio file with Electron API:",
          fileReadError
        );
        setIsLoading(false);
        return;
      }
    } catch (error: any) {
      console.error("❌ Error in handleGenerate:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAnalyseVideo = async () => {
    setIsLoading(true);
    const cutTimes = useCutStore.getState().cutTimes;
    const startTime = Date.now();

    const currentSegmentSettings = {
      weather,
      environment,
      groundTexture,
      groundMaterial,
      footwear,
      seed,
      qualitySounds,
      guidenceStrength,
      fullPrompt,
      negativePrompt,
      timeFrameSegment: timeFrameSegment,
    };

    const videoBuffer = await getLastSavedVideoBuffer();
    if (!videoBuffer) {
      console.error(
        "❌ No video buffer available. Please ensure a video is loaded."
      );
      setIsLoading(false);
      return;
    }

    const timestamp = new Date().getTime();
    const uniqueId = `${timestamp}_${selectedSegment.id}`;

    const currentFPS = getEffectiveFPS(overlays) || 30;
    const frameBasedStartTime = roundTimeToTwoDecimals(
      selectedSegment.from / currentFPS
    );
    const frameBasedEndTime = roundTimeToTwoDecimals(
      (selectedSegment.from + selectedSegment.durationInFrames) / currentFPS
    );

    const originalStartTime = roundTimeToTwoDecimals(selectedSegment.start);
    const originalEndTime = roundTimeToTwoDecimals(selectedSegment.end);

    const startTimeDiff = Math.abs(originalStartTime - frameBasedStartTime);
    const endTimeDiff = Math.abs(originalEndTime - frameBasedEndTime);
    const tolerance = 0.1;

    const accurateStartTime =
      startTimeDiff <= tolerance ? originalStartTime : frameBasedStartTime;
    const accurateEndTime =
      endTimeDiff <= tolerance ? originalEndTime : frameBasedEndTime;

    const segmentMetadata = {
      segmentId: selectedSegment.id,
      segmentName:
        selectedSegment.name || `Custom Segment ${selectedSegment.id}`,
      startTime: formatTimeToHHMMSSMS(accurateStartTime),
      endTime: formatTimeToHHMMSSMS(accurateEndTime),
      uniqueId,
    };

    const saveResult = await safeElectronAPI.onSaveVideo(
      videoBuffer,
      segmentMetadata
    );

    if (!saveResult.success) {
      console.error("❌ Failed to save video:", saveResult.error);
      setIsLoading(false);
      return;
    }

    let videoSizeBytes = 0;
    if (saveResult.filePath) {
      const projectDirectory = path.dirname(saveResult.filePath);
      updatePath(projectDirectory);

      try {
        const fileBuffer = await safeElectronAPI.readFileBuffer(
          saveResult.filePath
        );
        videoSizeBytes = fileBuffer.byteLength;
        setVideoSize(videoSizeBytes);
      } catch (error) {
        if (videoBuffer && videoBuffer.byteLength) {
          videoSizeBytes = videoBuffer.byteLength;
          setVideoSize(videoSizeBytes);
        }
      }
    }

    const saveDir = saveResult.metadata?.saveDir;
    if (!saveDir) {
      console.error("❌ No save directory available from metadata");
      setIsLoading(false);
      return;
    }

    const savedVideoFileName = saveResult.videoFileName;

    const reqAnalysVideo = {
      videoFileName: savedVideoFileName,
      destinationPath: saveDir,
      selectedSegment: {
        id: selectedSegment.id,
        name: selectedSegment.name,
        start: Math.round(accurateStartTime * 1000),
        end: Math.round(accurateEndTime * 1000),
      },
    };

    const response = await safeElectronAPI.runPythonDescription(reqAnalysVideo);

    // Fix: Access the nested data structure correctly
    const actualData = response.data?.data || response.data;

    if (response.success) {
      setShowPopup("ai");
      const endTime = Date.now();
      const processingTimeSeconds = (endTime - startTime) / 1000;
      setProcessingTime(processingTimeSeconds);

      // Calculate video length from selected segment
      // if (selectedSegment) {
      const currentFPS = getEffectiveFPS(overlays) || 30;
      const segmentDurationSeconds =
        selectedSegment.durationInFrames / currentFPS;
      setVideoLength(segmentDurationSeconds);
      // }

      setIsAnalyseVideoDone(true);
      const segmentMetadata = {
        segmentId: selectedSegment.id,
        segmentName:
          selectedSegment.name || `Custom Segment ${selectedSegment.id}`,
        startTime: formatTimeToHHMMSSMS(accurateStartTime),
        endTime: formatTimeToHHMMSSMS(accurateEndTime),
        audioFile: ``,
        characterSize: actualData.result.character_size,
        weather: actualData.result.weather,
        shortDescription: actualData.result.short_description,
        environment: actualData.result.environment,
        groundTexture: actualData.result.ground_texture,
        groundMaterial: actualData.result.ground_material,
        footwear: actualData.result.footwear,
        fullPrompt: actualData.result.prompt,
        negativePrompt: currentSegmentSettings.negativePrompt,
        seed: currentSegmentSettings.seed,
        qualitySounds: currentSegmentSettings.qualitySounds,
        guidenceStrength: currentSegmentSettings.guidenceStrength,
        uniqueId,
        timeFrameSegment: cutTimes,
        isAnalyseVideo: true,
        processingTime: processingTimeSeconds,
        segmentDurationSeconds: segmentDurationSeconds,
        videoSize: videoSizeBytes,
      };

      setWeather(actualData.result.weather);
      setEnvironment(actualData.result.environment);
      setGroundTexture(actualData.result.ground_texture);
      setGroundMaterial(actualData.result.ground_material);
      setFootwear(actualData.result.footwear);
      setCharacterSize(actualData.result.character_size);

      await safeElectronAPI.onSaveVideo(videoBuffer, segmentMetadata);
      setIsAnalyse(true);

      // Store the API response data first - access the nested data structure
      if (actualData?.analytic_list?.data) {
        // Use the nested data, not the wrapper object
        setApiAnalyticList(actualData.analytic_list.data);
      } else {
      }

      // Store the generated prompt

      if (actualData?.prompt) {
        setFullPrompt(actualData.prompt); // Set it to the textarea
      } else {
        // Check if prompt is nested elsewhere
        if (actualData?.result?.prompt) {
          setFullPrompt(actualData.result.prompt);
        }
      }

      // Store API result values to be processed after apiAnalyticList is updated
      if (actualData?.result) {
        // Also store the analytic list for validation
        setPendingApiResult({
          result: actualData.result,
          analyticList: actualData.analytic_list?.data,
        });
      } else {
      }

      // Update dataSegment store with the newly analyzed segment data
      const newSegmentData = {
        segmentId: selectedSegment.id,
        segmentName:
          selectedSegment.name || `Custom Segment ${selectedSegment.id}`,
        startTime: accurateStartTime,
        endTime: accurateEndTime,
        isAnalyseVideo: true,
        weather: actualData.result.weather,
        environment: actualData.result.environment,
        groundTexture: actualData.result.ground_texture,
        groundMaterial: actualData.result.ground_material,
        footwear: actualData.result.footwear,
        characterSize: actualData.result.character_size,
        fullPrompt: actualData.result.prompt || actualData.prompt,
        shortDescription: actualData.result.short_description,
        processingTime: processingTimeSeconds,
        segmentDurationSeconds: segmentDurationSeconds,
        videoSize: videoSizeBytes,
      };

      setSegment(newSegmentData);

      // Cache the analyzed segment data locally
      setAnalyzedSegmentsCache((prev) => {
        const newCache = new Map(prev);
        newCache.set(selectedSegment.id, newSegmentData);
        return newCache;
      });

      // Explicitly show popup after successful analysis
      setShowPopup("ai");

      // Small delay to ensure state propagation
      setTimeout(() => {
        setIsLoading(false);
      }, 100);
    } else {
      console.error("API Response failed:", response);
      setIsLoading(false);
    }
  };

  // Listen for menu analytics events
  useEffect(() => {
    const handleAnalyticsMenuClick: EventListener = (event: Event) => {
      const customEvent = event as CustomEvent;
      const fieldToFocus = customEvent.detail?.field;

      // Check if segment is selected before showing popup
      if (
        !selectedSegment ||
        selectedSegment.type === null ||
        selectedSegment.type === undefined ||
        selectedSegment.type !== "video"
      ) {
        setShowNoSegmentMessage(true);
        return;
      }
      setPage("home");
      setShowNoSegmentMessage(false);

      if (!isAnalyse) {
        // Trigger analytics and wait for popup to appear before focusing
        handleAnalyseVideo();

        // Wait for analytics to complete and popup to render
        const waitForAnalyticsPopup = () => {
          const analyticsPopup =
            document.querySelector('[data-testid="analytics-popup"]') ||
            document.querySelector(".MuiDialog-paper") ||
            document.querySelector('[role="dialog"]') ||
            document.querySelector('[data-testid="weather-select"]');

          if (analyticsPopup && fieldToFocus) {
            focusOnField(fieldToFocus);
          } else {
            setTimeout(waitForAnalyticsPopup, 200);
          }
        };

        // Start checking after a short delay
        setTimeout(waitForAnalyticsPopup, 1000);
      } else {
        // If analytics has run, focus on the specific field immediately
        if (fieldToFocus) {
          setTimeout(() => {
            focusOnField(fieldToFocus);
          }, 800);
        }
      }
    };

    const focusOnField = (fieldToFocus: string) => {
      const fieldMap: Record<string, { testId: string; heading: string }> = {
        weather: { testId: "weather-select", heading: "Weather" },
        environment: {
          testId: "environment-select",
          heading: "Environment",
        },
        groundTexture: {
          testId: "ground-texture-select",
          heading: "Ground Texture",
        },
        groundMaterial: {
          testId: "ground-material-select",
          heading: "Ground Material",
        },
        footwear: { testId: "footwear-select", heading: "Footwear" },
      };

      const fieldInfo = fieldMap[fieldToFocus];
      if (fieldInfo) {
        let selectElement = document.querySelector(
          `[data-testid="${fieldInfo.testId}"]`
        );

        if (selectElement) {
          const selectDiv =
            selectElement.querySelector(".MuiSelect-select") || selectElement;

          try {
            (selectDiv as HTMLElement).focus();

            setTimeout(() => {
              const mouseDownEvent = new MouseEvent("mousedown", {
                bubbles: true,
                cancelable: true,
                view: window,
                button: 0,
                clientX: 0,
                clientY: 0,
              });

              selectDiv.dispatchEvent(mouseDownEvent);

              setTimeout(() => {
                const mouseUpEvent = new MouseEvent("mouseup", {
                  bubbles: true,
                  cancelable: true,
                  view: window,
                  button: 0,
                  clientX: 0,
                  clientY: 0,
                });
                selectDiv.dispatchEvent(mouseUpEvent);

                setTimeout(() => {
                  const clickEvent = new MouseEvent("click", {
                    bubbles: true,
                    cancelable: true,
                    view: window,
                    button: 0,
                    clientX: 0,
                    clientY: 0,
                  });
                  selectDiv.dispatchEvent(clickEvent);
                }, 50);
              }, 50);
            }, 100);
          } catch (error) {
            // Silent error handling
          }
          return;
        }

        const heading = Array.from(
          document.querySelectorAll("h4, h3, h2, h1, div, span")
        ).find((el) => el.textContent?.trim() === fieldInfo.heading);

        if (heading) {
          const container = heading.parentElement;
          const selectDiv =
            container?.querySelector(".MuiSelect-select") ||
            container?.parentElement?.querySelector(".MuiSelect-select") ||
            container?.nextElementSibling?.querySelector(".MuiSelect-select");

          if (selectDiv) {
            (selectDiv as HTMLElement).focus();

            setTimeout(() => {
              const clickEvent = new MouseEvent("click", {
                bubbles: true,
                cancelable: true,
                view: window,
                button: 0,
              });
              selectDiv.dispatchEvent(clickEvent);
            }, 100);
            return;
          }
        }

        const allSelects = document.querySelectorAll(".MuiSelect-select");

        for (let i = 0; i < allSelects.length; i++) {
          const select = allSelects[i];
          const parent =
            select.closest('div[class*="flex flex-col gap-2"]') ||
            select.parentElement?.parentElement;
          const headingInParent = parent?.querySelector(
            "h4, h3, h2, h1, div, span"
          );
          if (
            headingInParent &&
            headingInParent.textContent?.trim() === fieldInfo.heading
          ) {
            (select as HTMLElement).focus();

            setTimeout(() => {
              const clickEvent = new MouseEvent("click", {
                bubbles: true,
                cancelable: true,
                view: window,
                button: 0,
              });
              select.dispatchEvent(clickEvent);
            }, 100);
            return;
          }
        }
      }
    };

    window.addEventListener("analytics-menu-click", handleAnalyticsMenuClick);

    return () => {
      window.removeEventListener(
        "analytics-menu-click",
        handleAnalyticsMenuClick
      );
    };
  }, [
    selectedSegment,
    isAnalyse,
    handleAnalyseVideo,
    setPage,
    setShowNoSegmentMessage,
  ]);

  // Main effect to manage analysis state based on selected segment
  useEffect(() => {
    // Don't reset state while analysis is in progress to avoid popup flicker
    if (isLoading) {
      return;
    }

    // Also don't reset immediately after analysis completes to avoid closing popup
    if (
      isAnalyseVideoDone &&
      selectedSegment?.id &&
      dataSegment?.segmentId === selectedSegment.id
    ) {
      return;
    }

    // Always reset first to ensure clean state (but only when not loading)
    setIsAnalyse(false);
    setIsAnalyseVideoDone(false); // Reset this too to prevent false positives
    setApiShortDescription("");

    // Close popup for unanalyzed segments (will be reopened if segment is analyzed)
    if (showPopup === "ai") {
      setShowPopup("");
    }

    if (!selectedSegment) {
      return;
    }

    // Check if current segment has been analyzed
    const segmentAnalyzed = isCurrentSegmentAnalyzed();

    if (segmentAnalyzed) {
      // Check cache first (highest priority)
      const cachedSegmentData = analyzedSegmentsCache.get(selectedSegment.id);
      if (cachedSegmentData) {
        setSegment(cachedSegmentData); // Update the store with cached data

        setIsAnalyseVideoDone(true);
        setIsAnalyse(true);

        // Load cached segment-specific data
        setApiShortDescription(cachedSegmentData.shortDescription ?? "");
        if (cachedSegmentData.weather) setWeather(cachedSegmentData.weather);
        if (cachedSegmentData.environment)
          setEnvironment(cachedSegmentData.environment);
        if (cachedSegmentData.groundTexture)
          setGroundTexture(cachedSegmentData.groundTexture);
        if (cachedSegmentData.groundMaterial)
          setGroundMaterial(cachedSegmentData.groundMaterial);
        if (cachedSegmentData.footwear) setFootwear(cachedSegmentData.footwear);
        if (cachedSegmentData.characterSize)
          setCharacterSize(cachedSegmentData.characterSize);
        if (cachedSegmentData.fullPrompt)
          setFullPrompt(cachedSegmentData.fullPrompt);
      } else if (dataSegment?.segmentId === selectedSegment.id) {
        setIsAnalyseVideoDone(true);
        setIsAnalyse(true);

        // Load segment-specific data
        setApiShortDescription(dataSegment?.shortDescription ?? "");
        if (dataSegment?.weather) setWeather(dataSegment.weather);
        if (dataSegment?.environment) setEnvironment(dataSegment.environment);
        if (dataSegment?.groundTexture)
          setGroundTexture(dataSegment.groundTexture);
        if (dataSegment?.groundMaterial)
          setGroundMaterial(dataSegment.groundMaterial);
        if (dataSegment?.footwear) setFootwear(dataSegment.footwear);
        if (dataSegment?.characterSize)
          setCharacterSize(dataSegment.characterSize);
        if (dataSegment?.fullPrompt) setFullPrompt(dataSegment.fullPrompt);
      } else {
        // Segment analyzed but dataSegment doesn't match - try to load data from projectData

        if (projectData?.segments?.aiVideoAnalytics) {
          const targetSegmentData = Object.values(
            projectData.segments.aiVideoAnalytics
          ).find(
            (seg: any) =>
              seg.segmentId === selectedSegment.id &&
              seg.isAnalyseVideo === true
          ) as any;

          if (targetSegmentData) {
            setSegment(targetSegmentData);

            // Load the data into UI
            setIsAnalyseVideoDone(true);
            setIsAnalyse(true);
            setApiShortDescription(targetSegmentData.shortDescription ?? "");
            if (targetSegmentData.weather)
              setWeather(targetSegmentData.weather);
            if (targetSegmentData.environment)
              setEnvironment(targetSegmentData.environment);
            if (targetSegmentData.groundTexture)
              setGroundTexture(targetSegmentData.groundTexture);
            if (targetSegmentData.groundMaterial)
              setGroundMaterial(targetSegmentData.groundMaterial);
            if (targetSegmentData.footwear)
              setFootwear(targetSegmentData.footwear);
            if (targetSegmentData.characterSize)
              setCharacterSize(targetSegmentData.characterSize);
            if (targetSegmentData.fullPrompt)
              setFullPrompt(targetSegmentData.fullPrompt);
          } else {
            // Fallback: segment detected as analyzed but no data available
            setIsAnalyse(true);
          }
        } else {
          // No project data available, try to load segment data by time

          const currentFPS = getEffectiveFPS(overlays) || 30;
          const frameBasedStartTime =
            Math.round((selectedSegment.from / currentFPS) * 100) / 100;
          const frameBasedEndTime =
            Math.round(
              ((selectedSegment.from + selectedSegment.durationInFrames) /
                currentFPS) *
                100
            ) / 100;

          // Try to load segment data using existing function
          findSegmentSettingsByTime(frameBasedStartTime, frameBasedEndTime);

          // Set minimal analysis state for now
          setIsAnalyse(true);
        }
      }

      // Reopen popup for analyzed segments
      setShowPopup("ai");
    } else {
      // Segment not analyzed
    }
  }, [
    selectedSegment?.id,
    dataSegment,
    projectData,
    isLoading,
    isCurrentSegmentAnalyzed,
    setIsAnalyse,
    setIsAnalyseVideoDone,
    showPopup,
    setShowPopup,
    analyzedSegmentsCache,
  ]);

  // REMOVED: Auto-show popup logic - now purely manual

  // REMOVED: Auto popup after analysis - will be handled manually in handleAnalyseVideo

  // Additional effect to ensure analytics list is loaded when we have a project with analyzed segments
  useEffect(() => {
    // If we have project data with analyzed segments but no analytics list, load it
    if (projectData?.segments?.aiVideoAnalytics && !apiAnalyticList) {
      const firstSegment = Object.values(
        projectData.segments.aiVideoAnalytics
      )[0] as any;
      if (firstSegment && firstSegment.weather) {
        const mockAnalyticsList: ApiAnalyticList = {
          character_size: ["small", "medium", "big"],
          weather: ["dry", "wet"],
          footwear: ["barefoot", "shoes", "boots", "sandals"],
          environment: [
            {
              name: "indoor",
              subtypes: [
                "soft",
                "hard",
                "carpet",
                "fabric",
                "cushion",
                "tile",
                "wood",
                "concrete",
              ],
            },
            {
              name: "outdoor",
              subtypes: [
                "soft",
                "hard",
                "grass",
                "sand",
                "dirt",
                "concrete",
                "stone",
                "pavement",
              ],
            },
          ],
        };
        setApiAnalyticList(mockAnalyticsList);
      }
    }
  }, [projectData]);

  // Debug effect to track important state changes
  useEffect(() => {
    // Track state changes for debugging
  }, [
    isAnalyse,
    apiAnalyticList,
    dataSegment,
    projectData,
    environment,
    footwear,
  ]);

  return (
    <div
      className={`flex lg:gap-3 xl:gap-6 justify-start ${
        (dataSegment?.isAnalyseVideo || isAnalyse) &&
        !showNoSegmentMessage &&
        showPopup === "ai"
          ? "items-stretch"
          : "items-start"
      }`}
    >
      <div className="relative">
        <div className="lg:w-9 xl:w-16 px-2 py-4 bg-gradient-to-bl from-stone-300/25 via-white/25 to-neutral-400/25 rounded-[40px] shadow-[inset_1px_1px_3px_0px_rgba(255,255,255,0.29)] outline-[0.50px] outline-offset-[-0.50px] backdrop-blur-[10px] flex flex-col justify-start items-center gap-3">
          <ItemSetting isActive={showPopup === "ai" && isAnalyse}>
            <Image
              onClick={() => {
                // Check if segment is selected before showing popup
                if (
                  !selectedSegment ||
                  selectedSegment.type === null ||
                  selectedSegment.type === undefined ||
                  selectedSegment.type !== "video"
                ) {
                  setShowNoSegmentMessage(true);
                  return;
                }
                setPage("home");
                setShowNoSegmentMessage(false);

                // Check if current segment is analyzed
                const segmentAnalyzed = isCurrentSegmentAnalyzed();

                if (segmentAnalyzed) {
                  // Segment already analyzed, toggle popup
                  setShowPopup(showPopup === "ai" ? "" : "ai");
                } else {
                  // Segment not analyzed yet, close any open popup and trigger analysis
                  setShowPopup(""); // Close popup immediately
                  handleAnalyseVideo();
                }
              }}
              src={sparkles}
              alt="sparkles"
              className="lg:w-3 lg:h-3 xl:w-5 xl:h-5 cursor-pointer"
            />
          </ItemSetting>
          <ItemSetting isActive={showPopup === "setting"}>
            <Image
              onClick={() => {
                setShowPopup(
                  showPopup === "" || showPopup !== "setting" ? "setting" : ""
                );
                setPage("home");
              }}
              src={setting}
              alt="setting"
              className="lg:w-3 lg:h-3 xl:w-5 xl:h-5 cursor-pointer"
            />
          </ItemSetting>
          <ItemSetting
            isActive={showPopup === "download"}
            isDisabled={!isHasAudioOverlay}
          >
            <Image
              onClick={() => {
                setPage("home");
                setShowPopup(
                  showPopup === "" || showPopup !== "download" ? "download" : ""
                );
              }}
              src={download}
              alt="download"
              className={`lg:w-3 lg:h-3 xl:w-5 xl:h-5  ${
                !isHasAudioOverlay && "pointer-events-none opacity-50"
              }`}
            />
          </ItemSetting>
          <ItemSetting>
            <Image
              src={user}
              alt="user"
              className="lg:w-3 lg:h-3 xl:w-5 xl:h-5 cursor-pointer"
            />
          </ItemSetting>
          <ItemSetting isActive={showPopup === "help"}>
            <Image
              onClick={() => {
                setPage("help");
              }}
              src={help}
              alt="help"
              className="lg:w-3 lg:h-3 xl:w-5 xl:h-5 cursor-pointer"
            />
          </ItemSetting>
        </div>
      </div>

      {selectedSegment &&
        selectedSegment.type === "video" &&
        showPopup === "ai" && (
          <PopupSetting>
            <div className="flex flex-col gap-4 text-white p-4">
              {isAnalyse && (
                <h2 className="text-sm font-bold font-inter leading-tight tracking-tight">
                  AI Video Analytics
                </h2>
              )}

              {/* <p className="text-sm font-normal font-inter leading-tight tracking-tight">
              {apiShortDescription ||
                "Analysis completed. Please see the details below."}
            </p> */}

              <div className="character-size">
                <h3 className="text-sm font-semibold font-inter leading-tight tracking-tight">
                  Character size
                </h3>
                {apiAnalyticList?.character_size &&
                  Array.isArray(apiAnalyticList.character_size) &&
                  apiAnalyticList.character_size.length > 0 && (
                    <FormControl>
                      <RadioGroup
                        row
                        aria-labelledby="demo-row-radio-buttons-group-label"
                        name="row-radio-buttons-group"
                        value={characterSize}
                        onChange={(e) => setCharacterSize(e.target.value)}
                      >
                        {(() => {
                          const characterSizeData =
                            apiAnalyticList.character_size;
                          // Safety check: ensure characterSizeData is an array
                          if (!Array.isArray(characterSizeData)) {
                            console.warn(
                              "characterSizeData is not an array:",
                              characterSizeData
                            );
                            return null;
                          }
                          return characterSizeData.map((size) => (
                            <FormControlLabel
                              key={size}
                              value={size}
                              control={
                                <Radio
                                  icon={<UncheckedIcon />}
                                  checkedIcon={<CheckedIcon />}
                                />
                              }
                              label={
                                size.charAt(0).toUpperCase() + size.slice(1)
                              }
                              sx={{
                                "& .MuiFormControlLabel-label": {
                                  fontSize: "14px",
                                  fontFamily: "Inter, sans-serif",
                                  color: "white",
                                },
                              }}
                            />
                          ));
                        })()}
                      </RadioGroup>
                    </FormControl>
                  )}
              </div>

              <div className="environtment-criteria flex flex-col gap-2">
                <div className="text-sm font-semibold font-inter leading-tight tracking-tight">
                  Environment criteria
                </div>
                <div className="pl-2 flex flex-col gap-2">
                  {weather && (
                    <SelectEnv
                      heading="Weather"
                      value={weather}
                      data={weatherItems}
                      onchange={(e: SelectChangeEvent<string>) =>
                        setWeather(e.target.value)
                      }
                      {...({
                        id: "weather-select",
                      } as any)}
                    />
                  )}

                  {environment && (
                    <SelectEnv
                      heading="Environment"
                      value={environment}
                      data={envItem}
                      onchange={(e: SelectChangeEvent<string>) => {
                        setEnvironment(e.target.value);
                        setGroundMaterial("-");
                      }}
                      {...({
                        id: "environment-select",
                      } as any)}
                    />
                  )}

                  {groundTexture && (
                    <SelectEnv
                      heading="Ground Texture"
                      value={groundTexture}
                      data={GroundTextureItems}
                      onchange={(e: SelectChangeEvent<string>) => {
                        setGroundTexture(e.target.value);
                        setGroundMaterial("-");
                      }}
                      {...({
                        id: "ground-texture-select",
                      } as any)}
                    />
                  )}

                  {groundMaterial && groundTexture && (
                    <SelectEnv
                      heading="Ground Material"
                      value={groundMaterial}
                      data={displayGroundMaterialsList}
                      onchange={(e: SelectChangeEvent<string>) =>
                        setGroundMaterial(e.target.value)
                      }
                      {...({
                        id: "ground-material-select",
                      } as any)}
                    />
                  )}

                  {footwear && (
                    <SelectEnv
                      heading="Footwear"
                      value={footwear}
                      data={FootwearItems}
                      onchange={(e: SelectChangeEvent<string>) =>
                        setFootwear(e.target.value)
                      }
                      {...({
                        id: "footwear-select",
                      } as any)}
                    />
                  )}

                  {/* Full prompt */}
                  {/* <div className="flex flex-col gap-2">
                  <h4 className=" text-gray-200 text-sm font-normal font-inter leading-tight tracking-tight mb-0">
                    Full Prompt
                  </h4>
                  <Box
                    component="form"
                    sx={{
                      "& .MuiTextField-root": {
                        m: 0,
                        width: "100%",
                        border: "none",
                        backgroundColor: "#1818184D",
                        "& .MuiInputBase-input": {
                          color: "white",
                          fontSize: "14px",
                          fontFamily: "Inter, sans-serif",
                          fontWeight: 400,
                        },
                        "& .MuiOutlinedInput-root": {
                          "& fieldset": {
                            border: "none",
                          },
                          "&:hover fieldset": {
                            border: "none",
                          },
                          "&.Mui-focused fieldset": {
                            border: "none",
                          },
                        },
                      },
                    }}
                    noValidate
                    autoComplete="off"
                  >
                    <TextField
                      id="outlined-multiline-static"
                      multiline
                      rows={4}
                      value={fullPrompt}
                      onChange={(e) => setFullPrompt(e.target.value)}
                      sx={{ color: "white" }}
                    />
                  </Box>
                </div> */}

                  {/* Video Information */}
                  <div className="flex flex-col gap-2">
                    <h4 className=" text-gray-200 text-sm font-normal font-inter leading-tight tracking-tight mb-0">
                      Video Information
                    </h4>
                    <div className="flex flex-col gap-3 justify-between mt-3 px-5">
                      <p className="text-header-2">
                        Processing Time: {formatDuration(processingTime)}
                      </p>
                      <p className="text-header-2">
                        Length of Video: {formatDuration(videoLength)}
                      </p>
                      <p className="text-header-2">
                        Size of Video: {formatFileSize(videoSize)}
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-col gap-4 mt-8">
                    <Button onClick={handleGenerate}>
                      {isLoading ? (
                        <Image
                          alt="loading"
                          src={loading}
                          width={25}
                          height={25}
                        />
                      ) : (
                        `Generate`
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </PopupSetting>
        )}

      {/* Loading Analyse */}
      {isLoading && !isAnalyse && (
        <PopupSetting>
          <div className="flex flex-col gap-4 text-white p-4">
            <div className="flex flex-col items-center justify-center gap-3 py-8">
              <Image alt="loading" src={loading} width={40} height={40} />
              <p className="text-sm font-normal font-inter leading-tight tracking-tight text-center">
                Analyzing video... Please wait.
              </p>
            </div>
          </div>
        </PopupSetting>
      )}
      {/* End Loading Analyse */}

      {showPopup === "download" && (
        <PopupSetting>
          <div className="flex flex-col gap-4 text-white p-4">
            <h2 className="text-sm font-bold font-inter leading-tight tracking-tight">
              Download
            </h2>

            <div className="flex flex-col gap-6">
              <div className="flex flex-col">
                <SelectEnv
                  heading="Download (Audio + Video)"
                  value={videoType}
                  data={VideoTypeItems}
                  onchange={(e: SelectChangeEvent<string>) =>
                    setVideoType(e.target.value)
                  }
                />
                <Button
                  customClass={`mt-4 `}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    downloadVideoWithAudio();
                  }}
                  disabled={isDownloadingVideo}
                >
                  {isDownloadingVideo ? (
                    <Image alt="loading" src={loading} width={25} height={25} />
                  ) : (
                    "Download"
                  )}
                </Button>

                {/* Video download messages */}
                {videoDownloadMessage.type === "success" && (
                  <div className="mt-3 text-sm font-inter leading-tight text-green-400">
                    Success! Your video has been downloaded.
                  </div>
                )}
                {videoDownloadMessage.type === "error" && (
                  <div className="mt-3 text-sm font-inter leading-tight text-red-400">
                    {videoDownloadMessage.message}
                  </div>
                )}
              </div>
              <div className="flex flex-col">
                <SelectEnv
                  heading="Download (Audio only)"
                  value={audioType}
                  data={AudioTypeItems}
                  onchange={(e: SelectChangeEvent<string>) =>
                    setAudioType(e.target.value)
                  }
                />
                <Button
                  customClass={`mt-4`}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    downloadAudioFromSecondRow();
                  }}
                >
                  {isDownloadingAudio ? (
                    <Image alt="loading" src={loading} width={25} height={25} />
                  ) : (
                    "Download"
                  )}
                </Button>

                {/* Audio download messages */}
                {downloadMessage.type === "success" && (
                  <div className="mt-3 text-sm font-inter leading-tight text-green-400">
                    {downloadMessage.message}
                  </div>
                )}
                {downloadMessage.type === "error" && (
                  <div className="mt-3 text-sm font-inter leading-tight text-red-400">
                    {downloadMessage.message}
                  </div>
                )}
              </div>
            </div>
          </div>
        </PopupSetting>
      )}

      {showPopup === "setting" && (
        <PopupSetting>
          <div className="flex flex-col gap-4 text-white p-4">
            <h2 className="text-sm font-bold font-inter leading-tight tracking-tight">
              Negative prompt
            </h2>
            <Box
              component="form"
              sx={{
                "& .MuiTextField-root": {
                  m: 0,
                  width: "100%",
                  border: "none",
                  backgroundColor: "#1818184D",
                  "& .MuiInputBase-input": {
                    color: "white",
                    fontSize: "14px",
                    fontFamily: "Inter, sans-serif",
                  },
                  "& .MuiOutlinedInput-root": {
                    "& fieldset": {
                      border: "none",
                    },
                    "&:hover fieldset": {
                      border: "none",
                    },
                    "&.Mui-focused fieldset": {
                      border: "none",
                    },
                  },
                },
              }}
              noValidate
              autoComplete="off"
            >
              <TextField
                id="outlined-multiline-static"
                multiline
                rows={6}
                value={negativePrompt}
                onChange={(e) => setNegativePrompt(e.target.value)}
                sx={{ color: "white" }}
              />
            </Box>

            <div className="flex flex-col gap-2">
              {/* Item slider */}
              <div className="flex flex-col">
                <h3>Seed: {seed}</h3>
                <Box sx={{ width: "80%" }}>
                  <Slider
                    onChange={(_, value) =>
                      setSeed(Array.isArray(value) ? value[0] : value)
                    }
                    aria-label="Default"
                    valueLabelDisplay="off"
                    value={seed}
                    max={100}
                  />
                </Box>
              </div>
              {/* Item slider */}
              <div className="flex flex-col">
                <h3>Quality of Sounds: {qualitySounds}</h3>
                <Box sx={{ width: "80%" }}>
                  <Slider
                    onChange={(_, value) =>
                      setQualitySounds(Array.isArray(value) ? value[0] : value)
                    }
                    aria-label="Default"
                    valueLabelDisplay="off"
                    value={qualitySounds}
                    max={100}
                  />
                </Box>
              </div>
              <div className="flex flex-col">
                <h3>Guidence Strength: {guidenceStrength}</h3>
                <Box sx={{ width: "80%" }}>
                  <Slider
                    onChange={(_, value) =>
                      setGuidenceStrength(
                        Array.isArray(value) ? value[0] : value
                      )
                    }
                    aria-label="Default"
                    valueLabelDisplay="off"
                    value={guidenceStrength}
                    max={50}
                  />
                </Box>
              </div>
            </div>
          </div>
        </PopupSetting>
      )}

      {/* No segment selected warning popup */}
      {showNoSegmentMessage && (
        <PopupSetting>
          <div className="flex flex-col gap-4 text-white p-4">
            <h2 className="text-sm font-bold font-inter leading-tight tracking-tight">
              Segment Required
            </h2>
            <div className="flex flex-col items-center justify-center gap-3 py-4">
              <div className="text-yellow-400 text-2xl">⚠️</div>
              <p className="text-sm font-normal font-inter leading-tight tracking-tight text-center text-yellow-400">
                Please select a segment first before analyzing the video.
              </p>
            </div>
          </div>
        </PopupSetting>
      )}
    </div>
  );
};

export default VideoSetting;
