# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
.claude
/node_modules
/.pnp
ext_weights
weights
mmaudio
videos
deployment
.venv
saved_projects
*.log
.pnp.js
.yarn/install-state.gz
dist
/__pycache__/*
__pycache__
*.pyc
models
# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# Render state
/tmp/render-state

# misc
.DS_Store
*.pem

# rendered videos
/public/rendered-videos/
/public/users

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
