import React from "react";
import { useCutStore } from "@/store/cutStore";
import { getEffectiveFPS } from "../../utils/fps-utils";

interface VideoSegmentMarkersProps {
  currentFrame: number;
  durationInFrames: number;
  overlays: any[];
}

const VideoSegmentMarkers: React.FC<VideoSegmentMarkersProps> = ({
  currentFrame,
  durationInFrames,
  overlays,
}) => {
  const { cutTimes } = useCutStore();
  const effectiveFPS = overlays.length > 0 ? getEffectiveFPS(overlays) : 30;

  // Convert CutTime to frame number
  const cutTimeToFrame = (cutTime: any) => {
    const totalSeconds =
      cutTime.hours * 3600 + cutTime.minutes * 60 + cutTime.seconds;
    const totalFrames = totalSeconds * effectiveFPS + cutTime.frames;
    return Math.round(totalFrames);
  };

  // If no overlays, don't show markers
  if (overlays.length === 0) {
    return null;
  }

  // Convert cut times to frame numbers and sort them
  const cutFrames = cutTimes
    .map((cutTime) => cutTimeToFrame(cutTime))
    .sort((a, b) => a - b);

  // Create segments with start and end frames
  const segments = [];

  if (cutFrames.length === 0) {
    // Single segment from 0 to end
    segments.push({
      index: 0,
      startFrame: 0,
      endFrame: durationInFrames,
      label: "SEGMENT 1",
    });
  } else {
    // First segment: 0 to first cut
    segments.push({
      index: 0,
      startFrame: 0,
      endFrame: cutFrames[0],
      label: "SEGMENT 1",
    });

    // Middle segments: between cuts
    for (let i = 0; i < cutFrames.length - 1; i++) {
      segments.push({
        index: i + 1,
        startFrame: cutFrames[i],
        endFrame: cutFrames[i + 1],
        label: `SEGMENT ${i + 2}`,
      });
    }

    // Last segment: last cut to end
    segments.push({
      index: cutFrames.length,
      startFrame: cutFrames[cutFrames.length - 1],
      endFrame: durationInFrames,
      label: `SEGMENT ${cutFrames.length + 1}`,
    });
  }

  // Find current segment
  const currentSegment =
    segments.find(
      (segment) =>
        currentFrame >= segment.startFrame && currentFrame < segment.endFrame
    ) || segments[segments.length - 1]; // Fallback to last segment if at end

  // Check if we're at start or end of current segment
  const isAtSegmentStart = currentFrame === currentSegment.startFrame; // Exact frame match only

  // For segment end detection:
  // All segments show end marker at endFrame - 1 (consistent with boundary navigation)
  const isLastSegment = currentSegment.index === segments.length - 1;
  const isAtSegmentEnd = currentFrame === currentSegment.endFrame - 1;

  return (
    <div className="absolute inset-0 pointer-events-none z-40">
      {/* Segment Start/End Markers */}
      {(isAtSegmentStart || isAtSegmentEnd) && (
        <>
          {/* Start marker - Bottom Left */}
          {isAtSegmentStart && (
            <div className="absolute bottom-4 left-4">
              <svg
                width="40"
                height="46"
                viewBox="0 0 130 149"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M29 120H130V149H0V0H29V120Z" fill="#22c55e" />
              </svg>
            </div>
          )}

          {/* End marker - Bottom Right */}
          {isAtSegmentEnd && (
            <div className="absolute bottom-4 right-4">
              <svg
                width="40"
                height="46"
                viewBox="0 0 130 149"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M101 120H0V149H130V0H101V120Z" fill="#ef4444" />
              </svg>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default VideoSegmentMarkers;
