import React, { use<PERSON>allback, useMemo } from "react";
import { AbsoluteFill } from "remotion";
import { Overlay } from "../types";
import { SortedOutlines } from "../components/selection/sorted-outlines";
import { Layer } from "../components/core/layer";

/**
 * Props for the Main component
 */
export type MainProps = {
  /** Array of overlay objects to be rendered */
  readonly overlays: Overlay[];
  /** Function to set the currently selected overlay ID */
  readonly setSelectedOverlayId: React.Dispatch<
    React.SetStateAction<number | null>
  >;
  /** Currently selected overlay ID, or null if none selected */
  readonly selectedOverlayId: number | null;
  /**
   * Function to update an overlay
   * @param overlayId - The ID of the overlay to update
   * @param updater - Function that receives the current overlay and returns an updated version
   */
  readonly changeOverlay: (
    overlayId: number,
    updater: (overlay: Overlay) => Overlay
  ) => void;
  /** Duration in frames of the composition */
  readonly durationInFrames: number;
  /** Frames per second of the composition */
  readonly fps: number;
  /** Width of the composition */
  readonly width: number;
  /** Height of the composition */
  readonly height: number;
  /** Base URL for media assets (optional) */
  readonly baseUrl?: string;
  // Array of segments that should have their audio muted
  readonly mutedSegments: Array<{
    id: number;
    startFrame: number;
    endFrame: number;
  }>;
};

const outer: React.CSSProperties = {
  backgroundColor: "#111827",
};

const layerContainer: React.CSSProperties = {
  overflow: "hidden",
  maxWidth: "3000px",
};

/**
 * Main component that renders a canvas-like area with overlays and their outlines.
 * Handles selection of overlays and provides a container for editing them.
 *
 * @param props - Component props of type MainProps
 * @returns React component that displays overlays and their interactive outlines
 */
export const Main: React.FC<MainProps> = ({
  overlays,
  setSelectedOverlayId,
  selectedOverlayId,
  changeOverlay,
  baseUrl,
  mutedSegments,
}) => {
  const onPointerDown = useCallback(
    (e: React.PointerEvent) => {
      if (e.button !== 0) {
        return;
      }

      // Check if clicked element is part of a video overlay
      const target = e.target as HTMLElement;
      const layerElement = target.closest("[data-overlay-type]");

      if (layerElement) {
        const overlayType = layerElement.getAttribute("data-overlay-type");
        const isVideoLocked =
          layerElement.getAttribute("data-overlay-locked") === "true";

        // If it's a video overlay, prevent selection and dragging
        if (overlayType === "video" || isVideoLocked) {
          e.preventDefault();
          e.stopPropagation();
          return;
        }
      }

      setSelectedOverlayId(null);
    },
    [setSelectedOverlayId]
  );

  // Filter overlays to prevent video overlays from being passed to SortedOutlines
  const draggableOverlays = useMemo(() => {
    return overlays.filter((overlay) => overlay.type !== "video");
  }, [overlays]);

  // Keep all overlays for rendering layers
  const allOverlays = overlays;

  return (
    <AbsoluteFill
      style={{
        backgroundColor: "#111827",
      }}
      onPointerDown={onPointerDown}
    >
      <AbsoluteFill style={layerContainer}>
        {allOverlays.map((overlay) => {
          return (
            <Layer
              key={overlay.id}
              overlay={overlay}
              selectedOverlayId={selectedOverlayId}
              baseUrl={baseUrl}
              mutedSegments={mutedSegments}
            />
          );
        })}
      </AbsoluteFill>
      {/* Only pass non-video overlays to SortedOutlines */}
      <SortedOutlines
        selectedOverlayId={selectedOverlayId}
        overlays={draggableOverlays} // Use filtered overlays
        setSelectedOverlayId={setSelectedOverlayId}
        changeOverlay={changeOverlay}
      />
    </AbsoluteFill>
  );
};
