import React from "react";
import { Audio } from "remotion";
import { SoundOverlay } from "../../../types";
import { toAbsoluteUrl } from "../../../utils/url-helper";

interface SoundLayerContentProps {
  overlay: SoundOverlay;
  baseUrl?: string;
}

export const SoundLayerContent: React.FC<SoundLayerContentProps> = ({
  overlay,
  baseUrl,
}) => {
  // Validate overlay has required properties
  if (!overlay.src) {
    return null;
  }

  // Check volume
  const volume = overlay.styles?.volume ?? 1;
  if (volume <= 0) {
    return null;
  }

  // Determine the audio source URL
  let audioSrc = overlay.src;

  // Handle URL resolution
  if (overlay.src.startsWith("/") && baseUrl) {
    audioSrc = `${baseUrl}${overlay.src}`;
  } else if (overlay.src.startsWith("/")) {
    audioSrc = toAbsoluteUrl(overlay.src);
  }

  const startFromSound = overlay.startFromSound || 0;

  try {
    return (
      <Audio
        src={audioSrc}
        startFrom={startFromSound}
        volume={volume}
        // 🔥 Add additional props for better compatibility
        muted={false}
        acceptableTimeShiftInSeconds={0.2}
      />
    );
  } catch (error) {
    return null;
  }
};

// Debug function to check if overlay matches SoundOverlay interface
export const debugSoundOverlay = (overlay: any): boolean => {
  const required = ["id", "type", "src", "from", "durationInFrames"];
  const missing = required.filter((prop) => !(prop in overlay));

  if (missing.length > 0) {
    return false;
  }
  return true;
};
