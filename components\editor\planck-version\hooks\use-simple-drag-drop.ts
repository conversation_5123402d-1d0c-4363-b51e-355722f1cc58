// hooks/use-simple-drag-drop.ts
"use client";

import { useState, useCallback, useRef } from 'react';

export interface DragDropState {
  isDragOver: boolean;
  dragCounter: number;
}

export interface DragDropHandlers {
  handleDragEnter: (e: React.DragEvent) => void;
  handleDragLeave: (e: React.DragEvent) => void;
  handleDragOver: (e: React.DragEvent) => void;
  handleDrop: (e: React.DragEvent) => void;
}

export interface UseSimpleDragDropOptions {
  onFilesDropped?: (files: File[]) => void;
  onDragStateChange?: (isDragOver: boolean) => void;
  acceptedTypes?: string[];
  maxFiles?: number;
  disabled?: boolean;
}

/**
 * Simple drag and drop hook that works with your existing context
 */
export const useSimpleDragDrop = (options: UseSimpleDragDropOptions = {}) => {
  const {
    onFilesDropped,
    onDragStateChange,
    acceptedTypes = [
      '.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v', // Video
      '.mp3', '.wav', '.aac', '.m4a', '.ogg', '.flac', // Audio
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg' // Images
    ],
    maxFiles = 10,
    disabled = false,
  } = options;

  const [state, setState] = useState<DragDropState>({
    isDragOver: false,
    dragCounter: 0,
  });

  const dragCounterRef = useRef(0);

  const isValidFile = useCallback((file: File): boolean => {
    const extension = '.' + file.name.split('.').pop()?.toLowerCase();
    return acceptedTypes.includes(extension);
  }, [acceptedTypes]);

  const updateDragState = useCallback((updates: Partial<DragDropState>) => {
    setState(prev => {
      const newState = { ...prev, ...updates };
      
      // Notify parent of drag state changes
      if (onDragStateChange && prev.isDragOver !== newState.isDragOver) {
        onDragStateChange(newState.isDragOver);
      }
      
      return newState;
    });
  }, [onDragStateChange]);

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (disabled) return;

    dragCounterRef.current += 1;
    
    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      updateDragState({ 
        isDragOver: true, 
        dragCounter: dragCounterRef.current 
      });
    }
  }, [disabled, updateDragState]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (disabled) return;

    dragCounterRef.current -= 1;
    
    if (dragCounterRef.current <= 0) {
      dragCounterRef.current = 0;
      updateDragState({ 
        isDragOver: false, 
        dragCounter: 0 
      });
    }
  }, [disabled, updateDragState]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (disabled) return;

    // Reset drag state
    dragCounterRef.current = 0;
    updateDragState({ 
      isDragOver: false, 
      dragCounter: 0 
    });

    try {
      const files = Array.from(e.dataTransfer.files);
      
      // Limit number of files
      const limitedFiles = files.slice(0, maxFiles);
      
      // Filter valid files
      const validFiles = limitedFiles.filter(isValidFile);

      // Process valid files
      if (validFiles.length > 0 && onFilesDropped) {
        onFilesDropped(validFiles);
      }

      // Log info about dropped files
      if (files.length > maxFiles) {
        console.warn(`Only first ${maxFiles} files will be processed`);
      }
      
      const invalidFiles = limitedFiles.filter(f => !isValidFile(f));
      if (invalidFiles.length > 0) {
        console.warn('Invalid files dropped:', invalidFiles.map(f => f.name));
      }
    } catch (error) {
      console.error('Error processing dropped files:', error);
    }
  }, [disabled, maxFiles, isValidFile, onFilesDropped, updateDragState]);

  const resetState = useCallback(() => {
    dragCounterRef.current = 0;
    setState({
      isDragOver: false,
      dragCounter: 0,
    });
  }, []);

  const handlers: DragDropHandlers = {
    handleDragEnter,
    handleDragLeave,
    handleDragOver,
    handleDrop,
  };

  return {
    state,
    handlers,
    resetState,
    isValid: !disabled,
  };
};

// Utility functions
export const getFileType = (file: File): 'video' | 'audio' | 'image' | 'unknown' => {
  const extension = '.' + file.name.split('.').pop()?.toLowerCase();
  
  if (['.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v'].includes(extension)) {
    return 'video';
  } else if (['.mp3', '.wav', '.aac', '.m4a', '.ogg', '.flac'].includes(extension)) {
    return 'audio';
  } else if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'].includes(extension)) {
    return 'image';
  }
  
  return 'unknown';
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};