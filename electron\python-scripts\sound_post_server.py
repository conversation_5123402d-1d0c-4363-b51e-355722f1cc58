#!/usr/bin/env python3
"""
Sound Post AI Server - FastAPI Version (Fixed)
"""

from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
import uvicorn
import json
import sys
import logging
import time
import traceback
from datetime import datetime

import requests

import os
from pathlib import Path
import shutil
import wave
import math
import aaf2
from fractions import Fraction
import cv2
from fastapi.responses import JSONResponse

from mmaudio_helper import initialize_model, initialize_post_processor, mmaudio_inference, cut_video_segment,analyze_video_content_with_openai,DESCRIPTIONS_TYPES
from video_auto_cut import get_video_segments
# initialize mmaudio
NET, FEATTURE_UTILS, SEQ_CFG = initialize_model(model='large_44k')
# initialize post processor
POST_PROCESSOR_MODEL = initialize_post_processor()


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Sound Post AI Server",
    description="AI Analysis Server for Electron App",
    version="1.0.0"
)

class HealthResponse(BaseModel):
    status: str
    message: str
    timestamp: float
    server_time: str
    models_loaded: bool
    version: str

# Global variables
models_loaded = True

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    logger.info("✅ Health check requested")
    
    return HealthResponse(
        status="healthy",
        message="AI server is running and ready",
        timestamp=time.time(),
        server_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        models_loaded=models_loaded,
        version="1.0.0"
    )

@app.get("/status")
async def server_status():
    """Detailed server status"""
    return {
        "status": "operational",
        "uptime": time.time(),
        "models_loaded": models_loaded,
        "active_models": ["sound_analysis", "audio_processing"] if models_loaded else [],
        "current_time": datetime.now().isoformat()
    }


def get_venv_python(venv_name="electron\\.venv"):
    """Get python executable from your specific venv"""
    venv_path = Path(venv_name)
    
    if os.name == 'nt':  # Windows
        python_exe = venv_path / "Scripts" / "python.exe"

    else:  # Linux/Mac
        python_exe = venv_path / "bin" / "python"
    
    return str(python_exe)


def convert_wav_to_aaf(wav_path: str, output_path: str = None, start_timecode_hours: int = 1, composition_fps: int = 24):
    """Convert WAV file to AAF format"""
    try:
        if not os.path.exists(wav_path):
            logger.error(f"❌ WAV file not found: {wav_path}")
            return False, f"WAV file not found: {wav_path}"
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(os.path.basename(wav_path))[0]
            output_dir = os.path.dirname(wav_path)
            output_path = os.path.join(output_dir, base_name + ".aaf")
        
        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Open the WAV file to read audio properties
        with wave.open(wav_path, 'rb') as wav_file:
            channels = wav_file.getnchannels()
            sample_rate = wav_file.getframerate()
            sample_width = wav_file.getsampwidth()  # in bytes per sample
            bit_depth = sample_width * 8
            num_frames = wav_file.getnframes()

        # Create a new AAF file and embed the WAV audio
        with aaf2.open(output_path, 'w') as f:
            # Create a Master Mob for the audio clip
            master_mob = f.create.MasterMob()
            base_name = os.path.splitext(os.path.basename(wav_path))[0]
            master_mob.name = base_name
            f.content.mobs.append(master_mob)

            # Import the WAV as embedded audio
            master_slot = master_mob.import_audio_essence(wav_path, composition_fps)

            # Find the associated SourceMob
            source_mob = None
            for mob in f.content.mobs:
                if mob is not master_mob and isinstance(mob, aaf2.mobs.SourceMob):
                    source_mob = mob
                    break

            # Safely access the descriptor
            desc = getattr(source_mob, "descriptor", None) if source_mob else None

            if desc:
                props = desc.properties()

                # Set Sample Rate
                if 'AudioSamplingRate' in props:
                    props['AudioSamplingRate'].value = Fraction(sample_rate, 1)
                elif 'SampleRate' in props:
                    props['SampleRate'].value = Fraction(sample_rate, 1)

                # Set Bit Depth
                if 'QuantizationBits' in props:
                    props['QuantizationBits'].value = bit_depth

                # Set Channel Count
                if 'Channels' in props:
                    props['Channels'].value = channels

                # Explicitly set Essence Compression to PCM
                if 'EssenceCompression' in props:
                    try:
                        pcm_def = f.dictionary.lookup_codec_def("PCM")
                        props['EssenceCompression'].value = pcm_def.auid
                    except Exception:
                        pass  # Safe to skip if lookup fails

                # Ensure frame-wrapped container format
                if 'ContainerFormat' in props:
                    try:
                        container_def = f.dictionary.lookup_containerdef("AAF")
                        props['ContainerFormat'].value = container_def.auid
                    except Exception:
                        pass

            # Create Composition Mob
            comp_mob = f.create.CompositionMob()
            comp_mob.name = f"{base_name}_Composition"
            comp_mob.usage = "Usage_TopLevel"
            f.content.mobs.append(comp_mob)

            # Create and add Timecode Track
            tc_slot = comp_mob.create_empty_sequence_slot(edit_rate=composition_fps, media_kind='timecode')

            tc_component = f.create.Timecode()
            tc_component.start = start_timecode_hours * 60 * 60 * composition_fps  # frames (1 hour at 24fps)
            tc_component.length = math.ceil(num_frames * composition_fps / sample_rate)

            tc_slot.segment.components.append(tc_component)

            # Create and add Audio Track
            audio_slot = comp_mob.create_sound_slot(edit_rate=composition_fps)
            comp_audio_clip = master_mob.create_source_clip(
                slot_id=master_slot.slot_id,
                length=master_slot.segment.length
            )
            audio_slot.segment = comp_audio_clip

        logger.info(f"✅ Successfully converted WAV to AAF: {output_path}")
        return True, output_path
        
    except Exception as e:
        logger.error(f"❌ Failed to convert WAV to AAF: {e}")
        return False, str(e)

def create_fake_wav(output_path: str, source_wav: str = "1.wav"):
    """Create a fake WAV file by copying an existing WAV file"""
    try:
        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Check if source file exists
        if not os.path.exists(source_wav):
            logger.error(f"❌ Source WAV file not found: {source_wav}")
            return False
        
        # Copy the existing WAV file
        shutil.copy2(source_wav, output_path)
        logger.info(f"✅ Copied fake WAV file: {source_wav} -> {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create fake WAV file: {e}")
        return False

@app.post("/analyze", response_model=dict)
async def analyze_audio(request: dict):
    """Main AI analysis endpoint"""
    try:

        print("request: ",request)
        
        request_id = request.get("request_id",None)
        destinationPath = request.get("destinationPath",None)
        videoFileName = request.get("videoFileName",None)
        parameters = request.get("parameters",None)

        duration = request.get("duration",None)
        skip_video_composite = request.get("skip_video_composite",False)
    
        mock_mode = request.get("mock_mode", False)
        
        video = destinationPath+"/"+videoFileName

        selectedSegment = request.get("selectedSegment",None)
        start_time = selectedSegment.get("start",0)
        end_time = selectedSegment.get("end",0)
        
        # Convert to seconds
        start_time = start_time / 1000
        end_time = end_time / 1000
        selectedSegment['start'] = start_time
        selectedSegment['end'] = end_time

        videoFileName = videoFileName.split(".")[0]
        output_audio_name = videoFileName+"_footstep"

        # NEW: Mock mode execution
        if mock_mode:

            # Set up output path
            if destinationPath:
                output_dir = os.path.join(destinationPath, "ai")
                os.makedirs(output_dir, exist_ok=True)
                output_file_path = os.path.join(output_dir, output_audio_name + ".wav")
            else:
                output_file_path = output_audio_name + ".wav"

            logger.info(f"🎭 Running in MOCK MODE - creating fake WAV file")
            
            # Create fake WAV file
            success = create_fake_wav(output_file_path)
            
            if success:
                response = {
                    "data": {
                        "destinationPath": output_dir if destinationPath else ".",
                        "generated_file": output_audio_name + ".wav",
                        "mock_mode": True
                    },
                    "message": "Mock analysis completed successfully (fake WAV file generated)",
                    "status": "success",
                    "success": True
                }
                logger.info("✅ Mock analysis completed successfully")
                return response
            else:
                response = {
                    "data": "",
                    "message": "Failed to create fake WAV file",
                    "status": "error",
                    "success": False
                }
                logger.error("❌ Mock analysis failed")
                return response
        

        data={
            'net': NET,
            'feature_utils': FEATTURE_UTILS,
            'seq_cfg': SEQ_CFG,
            'post_processor_model': POST_PROCESSOR_MODEL,
            'skip_video_composite': skip_video_composite,
            'full_precision': True
        }
        if video:
            data['video_path'] = video

        
        if output_audio_name:
            data['output'] = output_audio_name
            
        if duration:
            data['duration'] = duration
        
        if selectedSegment:
            data['selected_segment'] = selectedSegment
            
        if destinationPath:
            data['output_dir'] = os.path.join(destinationPath, "ai")
            
        
        # AI Analytic    
        if 'full_prompt' in parameters:
            data['full_prompt'] = parameters['full_prompt']        
        
        # settings
        if 'seed' in parameters:
            data['seed'] = parameters['seed']
        
        if 'guidance_strength' in parameters:
            data['guidance_strength'] = parameters['guidance_strength']
            
        if 'quality_sounds' in parameters:
            data['quality_sounds'] = parameters['quality_sounds']
            
        if 'negative_prompt' in parameters:
            data['negative_prompt'] = parameters['negative_prompt']
        
    
        # Execute
        save_path, video_save_path = mmaudio_inference(data)
    

        print("save_path: ",save_path)
        print("video_save_path: ",video_save_path)

        response = {
            "data":{
                "destinationPath": save_path,
                "video_save_path": video_save_path,
                "generated_file": output_audio_name+ ".wav",
            },
            "message": "Analysis completed successfully",
            "status":"success",
            "success": True
        }
        logger.info("Analysis completed from Python")
        return response

    except Exception as e:
        logger.error(f"❌ Analysis error: {e}")
        logger.error(f"❌ Analysis error: {traceback.format_exc()}")
        #traceback
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/description")
async def video_description(request: dict):
    """Description endpoint"""
    try:
        print("request: ",request)
        destinationPath = request.get("destinationPath",None)
        selected_segment = request.get('selectedSegment', None)        
        videoFileName = request.get('videoFileName', None)        
        start_ms = selected_segment.get('start', 0)
        end_ms = selected_segment.get('end', None)
        video_path = destinationPath+"/"+videoFileName
        if start_ms is None or end_ms is None:
            raise ValueError("Invalid segment time range")
        
        start_time = start_ms / 1000
        end_time = end_ms / 1000
  
        logger.info(f"Analyzing video_path with OpenAI: {video_path}")
        temp_video_path = cut_video_segment(str(video_path), start_time, end_time)
        
        logger.info(f"Analyzing temp_video_path with OpenAI: {temp_video_path}")
        analysis_result = analyze_video_content_with_openai(temp_video_path)
        base_prompt = "Only footstep sound effects. No voices, no dialogue, no human speech, no music. Just the sound of feet walking"
        params = []
        
        if analysis_result.get("character_size"):
            params.append(f"of {analysis_result.get('character_size')} size")
        
        if analysis_result.get("footwear"):
            params.append(f"wearing {analysis_result.get('footwear')}")
        
        if analysis_result.get("environment"):
            params.append(f"in an {analysis_result.get('environment')} setting")
        
        if analysis_result.get("weather"):
            params.append(f"during {analysis_result.get('weather')} conditions")
        
        if analysis_result.get("ground_texture") and analysis_result.get("ground_material"):
            params.append(f"on {analysis_result.get('ground_texture')} {analysis_result.get('ground_material')}")
        elif analysis_result.get("ground_material"):
            params.append(f"on {analysis_result.get('ground_material')}")
        
        # If we have parameters, enhance the prompt
        if params:
            prompt = f"{base_prompt}. A person {' '.join(params)}."
        else:
            prompt = base_prompt

        analysis_result['prompt'] = prompt

        return {
            "success": True,
            "data": {
                "result":analysis_result,
                "analytic_list":DESCRIPTIONS_TYPES,
            }
        }
    except Exception as e:
        logger.error(f"❌ Analysis error: {e}")
        logger.error(f"❌ Analysis error: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/convert/wav-to-aaf")
async def convert_wav_to_aaf_endpoint(request: dict):
    """Convert WAV file to AAF format"""
    try:
        wav_path = request.get("wav_path")
        output_path = request.get("output_path", None)  # Optional
        start_timecode_hours = request.get("start_timecode_hours", 1)
        composition_fps = request.get("composition_fps", 24)
        
        if not wav_path:
            raise HTTPException(status_code=400, detail="wav_path is required")
        
        # Convert the file
        success, result = convert_wav_to_aaf(
            wav_path=wav_path,
            output_path=output_path,
            start_timecode_hours=start_timecode_hours,
            composition_fps=composition_fps
        )
        
        if success:
            return {
                "success": True,
                "message": "WAV successfully converted to AAF",
                "aaf_path": result,
                "wav_path": wav_path
            }
        else:
            return {
                "success": False,
                "message": f"Conversion failed: {result}",
                "wav_path": wav_path
            }
            
    except Exception as e:
        logger.error(f"❌ WAV to AAF conversion error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/convert/batch-wav-to-aaf")
async def batch_convert_wav_to_aaf(request: dict):
    """Convert multiple WAV files to AAF format"""
    try:
        wav_files = request.get("wav_files", [])  # List of WAV file paths
        output_dir = request.get("output_dir", None)  # Optional output directory
        start_timecode_hours = request.get("start_timecode_hours", 1)
        composition_fps = request.get("composition_fps", 24)
        
        if not wav_files:
            raise HTTPException(status_code=400, detail="wav_files list is required")
        
        results = []
        successful_conversions = 0
        
        for wav_path in wav_files:
            try:
                # Generate output path if output_dir is specified
                if output_dir:
                    base_name = os.path.splitext(os.path.basename(wav_path))[0]
                    output_path = os.path.join(output_dir, base_name + ".aaf")
                else:
                    output_path = None
                
                success, result = convert_wav_to_aaf(
                    wav_path=wav_path,
                    output_path=output_path,
                    start_timecode_hours=start_timecode_hours,
                    composition_fps=composition_fps
                )
                
                if success:
                    successful_conversions += 1
                    results.append({
                        "wav_path": wav_path,
                        "aaf_path": result,
                        "success": True,
                        "message": "Converted successfully"
                    })
                else:
                    results.append({
                        "wav_path": wav_path,
                        "aaf_path": None,
                        "success": False,
                        "message": result
                    })
                    
            except Exception as file_error:
                results.append({
                    "wav_path": wav_path,
                    "aaf_path": None,
                    "success": False,
                    "message": str(file_error)
                })
        
        return {
            "success": True,
            "message": f"Batch conversion completed: {successful_conversions}/{len(wav_files)} files converted",
            "total_files": len(wav_files),
            "successful_conversions": successful_conversions,
            "failed_conversions": len(wav_files) - successful_conversions,
            "results": results
        }
        
    except Exception as e:
        logger.error(f"❌ Batch WAV to AAF conversion error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/description/types")
async def get_description_types():
    """
    Retrieves all valid values for the different descriptive parameters.
    """
    try:
        # Load the existing structure from the JSON file
        description_types_path = Path('./config/description_types.json')

        if description_types_path.exists():
            with open(description_types_path, 'r') as f:
                return json.load(f)

        # If file doesn't exist, create a new structure
        description_types = DESCRIPTIONS_TYPES

        # Save the formatted response to the existing JSON file
        with open(description_types_path, 'w') as f:
            json.dump(description_types, f, indent=4)

        logger.info(f"Updated description types in {description_types_path}")

        return description_types
    except Exception as e:
        logger.error(f"Error updating description types: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": f"Internal server error: {str(e)}",
                "data": None
            }
        )

# Video analysis functions are now imported from video_auto_cut.py

@app.post("/video-auto-cut")
async def get_video_auto_cut_segments(request: dict):
    """
    Analyze video and return timestamp segments for cutting
    Based on video-auto-cut repository logic using scene detection and audio analysis
    
    Request format:
    {
        "video_path": "E:\\Work\\DeepsLabs\\mochachai-fe-react\\electron\\saved_projects\\test941\\test941.mp4",
        "min_segment_length": 1.0,      // Changed from 0.3 to 1.0 (prevents over-filtering)
        "max_segments": 8,              // Changed from 5 to 8 (allow more segments)
        "scene_threshold": 10.0,        // Changed from 2 to 10.0 (this is the key - finds real scenes)
        "audio_tolerance": -40.0,
        "use_rgb_detect": false,
        "format": "simple"
  }
    
    Response format (simple):
    {
        "timestamps": "00:00:00-00:00:03, 00:00:06-00:00:07",
        "segment_count": 2
    }
    
    Response format (detailed):
    {
        "success": true,
        "data": {
            "segments": [
                {"start": "00:00:00.00", "end": "00:00:03.50"},
                {"start": "00:00:06.00", "end": "00:00:07.25"}
            ],
            "segment_count": 2,
            "total_duration": 30.5,
            "analysis_method": "content+audio",
            "formatted_timestamps": "00:00:00-00:00:03, 00:00:06-00:00:07",
            "video_path": "/path/to/video.mp4"
        }
    }
    """
    try:
        logger.info(f"📹 Video segments analysis request")
        
        # Extract parameters with optimized defaults for better cutting
        
        video_path = request.get("video_path")
        min_segment_length = request.get("min_segment_length", 1.0)    # Shorter segments allowed (was 2.0)
        max_segments = request.get("max_segments", 15)                 # More segments allowed (was 10)
        scene_threshold = request.get("scene_threshold", 10.0)         # Low threshold for real scene detection (avoids fallback)
        audio_tolerance = request.get("audio_tolerance", -40.0)
        use_rgb_detect = request.get("use_rgb_detect", False)         # Use Content Detection (works better than RGB)
        response_format = request.get("format", "detailed")
        
        logger.info(f"🎯 Parameters: video_path={video_path}, threshold={scene_threshold}, min_length={min_segment_length}, max_segments={max_segments}, audio_tolerance={audio_tolerance}, rgb={use_rgb_detect}")
        if min_segment_length :
            min_segment_length = min_segment_length / 1000
            print('min-segment', min_segment_length)
        # Validate input
        if not video_path:
            return {
                "success": False,
                "data": {},
                "error_msg": "video_path is required."
            }
        
        # Validate parameter ranges
        if min_segment_length <= 0:
            return {
                "success": False,
                "data": {},
                "error_msg": "min_segment_length must be greater than 0"
            }
        
        if max_segments <= 0:
            return {
                "success": False,
                "data": {},
                "error_msg": "max_segments must be greater than 0"
            }
        
        if scene_threshold <= 0:
            return {
                "success": False,
                "data": {},
                "error_msg": "scene_threshold must be greater than 0"
            }
        
        if response_format not in ["simple", "detailed"]:
            return {
                "success": False,
                "data": {},
                "error_msg": "format must be 'simple' or 'detailed'"
            }
        
        # Use the video_auto_cut module for analysis
        try:
            result = get_video_segments(
                video_path,
                simple_format=False,  # Always get detailed first
                min_segment_length=min_segment_length,
                max_segments=max_segments,
                scene_threshold=scene_threshold,
                audio_tolerance=audio_tolerance,
                use_rgb_detect=use_rgb_detect
            )
            
        except FileNotFoundError as e:
            logger.error(f"❌ Video file not found: {video_path}")
            return {
                "success": False,
                "data": {},
                "error_msg": f"Video file not found: {video_path}"
            }
        except Exception as e:
            # Log the specific error for debugging
            logger.error(f"❌ Video analysis failed: {str(e)}")
            logger.error(f"❌ Error type: {type(e).__name__}")
            
            # Return specific error messages based on the exception
            error_message = str(e)
            if "SceneDetect" in error_message:
                error_msg = f"Scene detection library not available: {error_message}"
            elif "Audio processing" in error_message:
                error_msg = f"Audio processing libraries not available: {error_message}"
            elif "Scene detection failed" in error_message:
                error_msg = f"{error_message} - Please adjust parameters below"
            elif "Failed to get video duration" in error_message:
                error_msg = f"Cannot read video file or determine duration: {error_message}"
            else:
                error_msg = f"Video analysis failed: {error_message}"
            print( {
                "success": False,
                "data": {},
                "error_msg": error_msg
                }
            )
            return {
                "success": False,
                "data": {},
                "error_msg": error_msg
            }
        
        # Check duration limit
        if result.get("total_duration", 0) > 3600:  # 1 hour limit
            return {
                "success": False,
                "data": {},
                "error_msg": "Video duration too long. Maximum 1 hour supported."
            }
        
        
        
        
        # Create timesplit array from timestamps
        def create_timesplit(formatted_timestamps: str, video_duration: float) -> list:
            """Create timesplit array from formatted timestamps"""
            if not formatted_timestamps:
                return []
            
            timesplit = []
            segments = formatted_timestamps.split(", ")
            
            for segment in segments:
                if "-" in segment:
                    end_time = segment.split("-")[1]  # Get end time (00:00:02)
                    timesplit.append(end_time)
            
            # Remove last element if it's close to video duration (within 1 second)
            if timesplit and video_duration:
                last_time = timesplit[-1]
                # Convert HH:MM:SS to seconds
                time_parts = last_time.split(":")
                last_seconds = int(time_parts[0]) * 3600 + int(time_parts[1]) * 60 + int(time_parts[2])
                
                if abs(last_seconds - video_duration) <= 1:
                    timesplit.pop()
            
            return timesplit

        formatted_timestamps = create_timesplit(result["formatted_timestamps"], result["total_duration"])
        
        if not formatted_timestamps:
            return {
                "success": False,
                "data": {},
                "error_msg": "The system cannot determine the cut point, please adjust the parameters below"
            }
        
        # Format response based on requested format
        if response_format == "simple":
            response = {
                "success": True,
                "data":{
                    "timestamps": result["formatted_timestamps"],
                    "segment_count": result["segment_count"],
                    "video_duration": result["total_duration"],
                    "timesplit": formatted_timestamps
                }
            }
        else:
            # Default detailed format - return the full result with success wrapper
            result["timesplit"] = create_timesplit(result["formatted_timestamps"], result["total_duration"])
            response = {
                "success": True,
                "data": result
            }
        
        logger.info(f"✅ Video auto-cut analysis completed: {result['segment_count']} segments for {video_path}")
        return response
        
    except Exception as e:
        logger.error(f"❌ Video segments analysis error: {e}")
        logger.error(f"❌ Video segments analysis error: {traceback.format_exc()}")
        return {
            "success": False,
            "data": {},
            "error_msg": f"Unexpected error: {str(e)}"
        }


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Sound Post AI Server",
        "status": "running",
        "docs": "/docs",
        "health": "/health",
        "time": datetime.now().isoformat()
    }

# CORS middleware
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def main():
    """Main entry point"""
    port = 8000
    host = "127.0.0.1"
    
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            logger.error("Invalid port number")
            sys.exit(1)

    
    
    logger.info("🐍 Starting Sound Post AI Server with FastAPI...")
    
    try:
        uvicorn.run(
            app,
            host=host,
            port=port,
            log_level="info",
            access_log=False,
            timeout_keep_alive=0,  # Disable keep-alive timeout
        )
    except KeyboardInterrupt:
        logger.info("🛑 Server shutdown requested")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()