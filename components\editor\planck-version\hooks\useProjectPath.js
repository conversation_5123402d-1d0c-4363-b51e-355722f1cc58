import { useCallback, useRef, useState } from "react";

export function useProjectPath(initialPath) {
  const [projectUrl, setProjectUrl] = useState(initialPath);
  const pathRef = useRef(initialPath);

  const updatePath = useCallback((newPath) => {
    if (newPath && newPath !== pathRef.current) {
      pathRef.current = newPath;
      setProjectUrl(newPath);
      console.log("Path updated:", newPath);
    }
  }, []);

  return { projectUrl, pathRef, updatePath };
}
