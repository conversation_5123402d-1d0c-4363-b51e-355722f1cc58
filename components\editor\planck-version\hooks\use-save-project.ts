import { useEffect } from "react";
import { delayRender, continueRender } from "remotion";

export const useSaveProject = (videoSrc?: string, shouldRun: boolean = false) => {
// export const useSaveProject = (videoSrc?: string) => {
  useEffect(() => {
    if (!shouldRun || !videoSrc) return;

    const handle = delayRender("Preloading video");

    const video = document.createElement("video");
    video.src = videoSrc;

    const handleLoadedMetadata = () => {
      console.log(`Video metadata loaded INI DARI HOOKS: ${videoSrc}`);
      continueRender(handle);
    };

    const handleError = (error: ErrorEvent) => {
      console.error(`Error loading video ${videoSrc}:`, error);
      continueRender(handle);
    };

    video.addEventListener("loadedmetadata", handleLoadedMetadata);
    video.addEventListener("error", handleError);

    return () => {
      video.removeEventListener("loadedmetadata", handleLoadedMetadata);
      video.removeEventListener("error", handleError);
      continueRender(handle);
    };
  }, [videoSrc, shouldRun]);
};
