// utils/metadata.ts

/**
 * Menyimpan informasi hasil save video ke local state / logika tambahan
 */
export const handleVideoSaveSuccess = (result: any) => {
  console.log("Video berhasil disimpan:");
  console.log("Path Video:", result.filePath);
  console.log("Path Metadata:", result.metaPath);

  // Kamu bisa tambah logika lain seperti:
  // - menyimpan ke localStorage
  // - update ke context/store
  // - tracking analytics, dsb.

  // Contoh: simpan ke localStorage
  localStorage.setItem("lastSavedVideo", JSON.stringify({
    file: result.filePath,
    metadata: result.metaPath,
    savedAt: new Date().toISOString(),
  }));
};
