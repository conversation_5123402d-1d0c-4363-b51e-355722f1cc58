// hooks/use-cut-point-adjustment.ts
import { useEffect, useCallback, useState } from 'react';
import { Overlay } from '../types';

interface CutPointState {
  overlayId: number | null;
  edge: 'start' | 'end' | null;
}

interface UseCutPointAdjustmentProps {
  selectedOverlayId: number | null;
  overlays: Overlay[];
  onOverlayChange: (updatedOverlay: Overlay) => void;
  isPlaying?: boolean;
}

export const useCutPointAdjustment = ({
  selectedOverlayId,
  overlays,
  onOverlayChange,
  isPlaying = false,
}: UseCutPointAdjustmentProps) => {
  
  const [activeCutPoint, setActiveCutPoint] = useState<CutPointState>({
    overlayId: null,
    edge: null,
  });
  
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Don't handle during playback
    if (isPlaying) return;
    
    // Only handle Shift + Arrow keys
    if (!event.shiftKey || !['ArrowLeft', 'ArrowRight'].includes(event.key)) {
      return;
    }

    // Only if an overlay is selected and we have an active cut point
    if (!selectedOverlayId || !activeCutPoint.overlayId || !activeCutPoint.edge) return;

    const selectedOverlay = overlays.find(overlay => overlay.id === selectedOverlayId);
    if (!selectedOverlay) return;

    event.preventDefault();
    event.stopPropagation();

    const isMovingRight = event.key === 'ArrowRight';
    
    // Find overlays on the same row
    const overlaysOnSameRow = overlays.filter(
      overlay => overlay.row === selectedOverlay.row && overlay.id !== selectedOverlay.id
    ).sort((a, b) => a.from - b.from);

    if (activeCutPoint.edge === 'start') {
      // Adjusting the START edge (left side) of the selected overlay
      if (!isMovingRight) {
        // Shift + Left Arrow: Move start point left (extend overlay leftward)
        const newStartFrame = Math.max(0, selectedOverlay.from - 1);
        
        // Find the previous overlay that ends exactly where this one starts
        const previousOverlay = overlaysOnSameRow.find(
          overlay => overlay.from + overlay.durationInFrames === selectedOverlay.from
        );
        
        if (previousOverlay) {
          // Shrink the previous overlay by 1 frame
          onOverlayChange({
            ...previousOverlay,
            durationInFrames: Math.max(1, previousOverlay.durationInFrames - 1),
          });
        }
        
        // Extend the selected overlay leftward
        onOverlayChange({
          ...selectedOverlay,
          from: newStartFrame,
          durationInFrames: selectedOverlay.durationInFrames + (selectedOverlay.from - newStartFrame),
        });
        
      } else {
        // Shift + Right Arrow: Move start point right (trim from beginning)
        const newStartFrame = selectedOverlay.from + 1;
        const newDuration = selectedOverlay.durationInFrames - 1;
        
        // Don't allow duration to become too small
        if (newDuration < 1) return;
        
        // Find the previous overlay that should be extended
        const previousOverlay = overlaysOnSameRow.find(
          overlay => overlay.from + overlay.durationInFrames === selectedOverlay.from
        );
        
        if (previousOverlay) {
          // Extend the previous overlay by 1 frame
          onOverlayChange({
            ...previousOverlay,
            durationInFrames: previousOverlay.durationInFrames + 1,
          });
        }
        
        // Trim the selected overlay from the beginning
        onOverlayChange({
          ...selectedOverlay,
          from: newStartFrame,
          durationInFrames: newDuration,
        });
      }
    } else if (activeCutPoint.edge === 'end') {
      // Adjusting the END edge (right side) of the selected overlay
      if (!isMovingRight) {
        // Shift + Left Arrow: Move end point left (trim from end)
        const newDuration = Math.max(1, selectedOverlay.durationInFrames - 1);
        
        // Find the next overlay that starts exactly where this one ends
        const nextOverlay = overlaysOnSameRow.find(
          overlay => overlay.from === selectedOverlay.from + selectedOverlay.durationInFrames
        );
        
        if (nextOverlay) {
          // Extend the next overlay leftward
          onOverlayChange({
            ...nextOverlay,
            from: nextOverlay.from - 1,
            durationInFrames: nextOverlay.durationInFrames + 1,
          });
        }
        
        // Trim the selected overlay from the end
        onOverlayChange({
          ...selectedOverlay,
          durationInFrames: newDuration,
        });
        
      } else {
        // Shift + Right Arrow: Move end point right (extend overlay rightward)
        const nextOverlay = overlaysOnSameRow.find(
          overlay => overlay.from === selectedOverlay.from + selectedOverlay.durationInFrames
        );
        
        if (nextOverlay) {
          // Shrink the next overlay by 1 frame
          const newNextDuration = Math.max(1, nextOverlay.durationInFrames - 1);
          onOverlayChange({
            ...nextOverlay,
            from: nextOverlay.from + 1,
            durationInFrames: newNextDuration,
          });
        }
        
        // Extend the selected overlay rightward
        onOverlayChange({
          ...selectedOverlay,
          durationInFrames: selectedOverlay.durationInFrames + 1,
        });
      }
    }
  }, [selectedOverlayId, overlays, onOverlayChange, isPlaying, activeCutPoint]);

  useEffect(() => {
    // Add event listener to document for global keyboard handling
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);
  
  // Clear active cut point when selection changes
  useEffect(() => {
    if (!selectedOverlayId || activeCutPoint.overlayId !== selectedOverlayId) {
      setActiveCutPoint({ overlayId: null, edge: null });
    }
  }, [selectedOverlayId, activeCutPoint.overlayId]);
  
  const selectCutPoint = useCallback((overlayId: number, edge: 'start' | 'end') => {
    setActiveCutPoint({ overlayId, edge });
  }, []);
  
  return {
    activeCutPoint,
    selectCutPoint,
  };
};