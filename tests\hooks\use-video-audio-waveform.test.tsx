import { renderHook, waitFor } from "@testing-library/react";
import { useVideoAudioWaveform } from "@/components/editor/planck-version/hooks/use-video-audio-waveform";

// Mock the Web Audio API
const mockAudioContext = {
  decodeAudioData: jest.fn(),
  close: jest.fn(),
};

const mockAudioBuffer = {
  sampleRate: 44100,
  getChannelData: jest.fn(),
};

// Mock fetch
global.fetch = jest.fn();

// Mock AudioContext
global.AudioContext = jest.fn(() => mockAudioContext) as any;

describe("useVideoAudioWaveform", () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Setup default mocks
    (global.fetch as jest.Mock).mockResolvedValue({
      arrayBuffer: () => Promise.resolve(new ArrayBuffer(1024)),
    });

    mockAudioContext.decodeAudioData.mockResolvedValue(mockAudioBuffer);

    // Mock channel data with some sample audio data
    const sampleData = new Float32Array(44100); // 1 second of audio at 44.1kHz
    for (let i = 0; i < sampleData.length; i++) {
      sampleData[i] = Math.sin((2 * Math.PI * 440 * i) / 44100) * 0.5; // 440Hz sine wave
    }
    mockAudioBuffer.getChannelData.mockReturnValue(sampleData);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it("should return null waveform data when no src is provided", () => {
    const { result } = renderHook(() =>
      useVideoAudioWaveform(undefined, 0, 300)
    );

    expect(result.current.waveformData).toBeNull();
    expect(result.current.isLoading).toBe(false);
  });

  it("should process video audio and return waveform data", async () => {
    const { result } = renderHook(() =>
      useVideoAudioWaveform("test-video.mp4", 0, 300, {
        numPoints: 50,
        fps: 30,
      })
    );

    // Initially should be loading
    expect(result.current.isLoading).toBe(true);
    expect(result.current.waveformData).toBeNull();

    // Wait for processing to complete
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Should have waveform data
    expect(result.current.waveformData).not.toBeNull();
    expect(result.current.waveformData?.peaks).toHaveLength(50);
    expect(result.current.waveformData?.length).toBeGreaterThan(0);
  });

  it("should handle videoStartTime offset for split videos", async () => {
    const { result } = renderHook(() =>
      useVideoAudioWaveform("test-video.mp4", 150, 150, {
        numPoints: 30,
        fps: 30,
      })
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.waveformData).not.toBeNull();
    expect(result.current.waveformData?.peaks).toHaveLength(30);
  });

  it("should provide fallback waveform data on error", async () => {
    // Mock fetch to fail
    (global.fetch as jest.Mock).mockRejectedValue(new Error("Network error"));

    const { result } = renderHook(() =>
      useVideoAudioWaveform("invalid-video.mp4", 0, 300)
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Should have fallback data
    expect(result.current.waveformData).not.toBeNull();
    expect(result.current.waveformData?.peaks).toHaveLength(100); // default numPoints
    expect(
      result.current.waveformData?.peaks.every((peak) => peak >= 0 && peak <= 1)
    ).toBe(true);
  });

  it("should handle audio decoding errors gracefully", async () => {
    // Mock decodeAudioData to fail
    mockAudioContext.decodeAudioData.mockRejectedValue(
      new Error("Decode error")
    );

    const { result } = renderHook(() =>
      useVideoAudioWaveform("test-video.mp4", 0, 300)
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Should have fallback data
    expect(result.current.waveformData).not.toBeNull();
    expect(result.current.waveformData?.peaks.length).toBeGreaterThan(0);
  });

  it("should cleanup properly when component unmounts", () => {
    const { unmount } = renderHook(() =>
      useVideoAudioWaveform("test-video.mp4", 0, 300)
    );

    unmount();

    // Should not throw any errors during cleanup
    expect(true).toBe(true);
  });

  it("should handle different numPoints configurations", async () => {
    const { result } = renderHook(() =>
      useVideoAudioWaveform("test-video.mp4", 0, 300, {
        numPoints: 200,
        fps: 30,
      })
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.waveformData?.peaks).toHaveLength(200);
  });

  it("should apply silence threshold correctly", async () => {
    // Create audio data with silent and loud sections
    const sampleData = new Float32Array(44100); // 1 second of audio

    // First half: silence (very low amplitude)
    for (let i = 0; i < 22050; i++) {
      sampleData[i] = Math.random() * 0.005; // Very quiet noise
    }

    // Second half: loud audio
    for (let i = 22050; i < 44100; i++) {
      sampleData[i] = Math.sin((2 * Math.PI * 440 * (i - 22050)) / 44100) * 0.5; // 440Hz sine wave
    }

    mockAudioBuffer.getChannelData.mockReturnValue(sampleData);

    const { result } = renderHook(() =>
      useVideoAudioWaveform("test-video.mp4", 0, 300, {
        numPoints: 100,
        fps: 30,
        silenceThreshold: 0.01, // Threshold for silence detection
      })
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    const peaks = result.current.waveformData?.peaks || [];

    // First half should have mostly zeros (silent sections)
    const firstHalf = peaks.slice(0, 50);
    const silentPeaks = firstHalf.filter((peak) => peak === 0);
    expect(silentPeaks.length).toBeGreaterThan(30); // Most should be silent

    // Verify that we have both silent and loud sections
    const totalSilentPeaks = peaks.filter((peak) => peak === 0).length;
    const totalLoudPeaks = peaks.filter((peak) => peak > 0).length;

    expect(totalSilentPeaks).toBeGreaterThan(0); // Should have some silent sections
    expect(totalLoudPeaks).toBeGreaterThan(0); // Should have some loud sections
    expect(totalSilentPeaks + totalLoudPeaks).toBe(peaks.length); // All peaks accounted for
  });
});
