"use client";
import { useEffect, useState, useCallback, useRef } from "react";
import { useEditorContext } from "../../contexts/editor-context";
import {
  getLastSavedVideoBuffer,
  saveVideoProject,
} from "../../utils/save-project";
import { handleVideoSaveSuccess } from "../../utils/metadata";
import { useProjectManager } from "@/store/modalStore";
import { useExportModal } from "@/store/modalExportStore";
import { useExportMarkersModal } from "@/store/modalExportMarkersStore";
import { useSegmentStore } from "@/store/segmentStore";
import { useProjectBufferStore } from "@/store/convertBuffer";
import { useCompositionDuration } from "../../hooks/use-composition-duration";
import { useTimeline } from "../../contexts/timeline-context";
import { ZOOM_CONSTRAINTS } from "../../constants";
import {
  SoundOverlay,
  OverlayType,
  TextOverlay,
  ClipOverlay,
} from "../../types";
import { getEffectiveFPS } from "../../utils/fps-utils";
import { useAppContext } from "../../hooks/useAppContext";
import { useDataVideoStore } from "@/store/dataVideo";
import { useCutStore } from "@/store/cutStore";

/**
 * MenuHandler component manages Electron menu events and provides
 * the same functionality that was previously in the EditorHeader
 */
export function MenuHandler() {
  const editorContext = useEditorContext();
  const {
    deleteOverlay,
    overlays,
    addOverlay,
    handleFilesAdded,
    splitOverlay,
    setCurrentFrame,
    changeOverlay,
  } = editorContext;

  const { setIsAnalyseVideoDone } = useAppContext();
  const dataSegment = useDataVideoStore((state) => state.segment);
  const [projectDataForSplitting, setProjectDataForSplitting] = useState<
    any | null
  >(null);

  const timeFrameSegment = useSegmentStore((state) => state.timeFrameSegment);
  const {
    removeRow,
    visibleRows,
    zoomScale,
    setZoomScale,
    setScrollPosition,
    resetOverlays,
    timelineRef,
    addRow,
    setIsProjectLoading,
  } = useTimeline();

  const { openImportMarkersModal, setCurrentVideoFPS } = useProjectManager();
  const { openExportModal } = useExportModal();
  const { openExportMarkersModal } = useExportMarkersModal();

  // Update save menu items when overlays change
  useEffect(() => {
    if (window.electronAPI && "updateSaveMenuItems" in window.electronAPI) {
      (window.electronAPI as any).updateSaveMenuItems(overlays.length > 0);
    }
  }, [overlays]);

  const clearCutTimes = useCutStore((state) => state.clearCutTimes);
  const { addCutTime } = useCutStore();

  const newProject = useCallback(async () => {
    try {
      // Set loading state to hide gap indicators
      setIsProjectLoading(true);
      clearCutTimes();
      // Clear all overlays first
      overlays.forEach((overlay) => {
        deleteOverlay(overlay.id);
      });

      // Reset overlays state
      resetOverlays();

      // Clear segment store
      useSegmentStore.getState().setTimeFrameSegment("");

      // Clear project buffer store
      const projectBufferStore = useProjectBufferStore.getState();
      if (projectBufferStore.videoUrl) {
        URL.revokeObjectURL(projectBufferStore.videoUrl); // Clean up blob URL
      }
      (projectBufferStore.setVideoUrl as any)(null);
      (projectBufferStore.setVideoBuffer as any)(null);

      // Clear data video store
      (useDataVideoStore.getState().setSegment as any)(null);

      // Clear analysis state
      setIsAnalyseVideoDone(false);

      let currentRows = visibleRows;
      while (currentRows > 1) {
        removeRow();
        currentRows--; // Manually decrement since visibleRows might not update immediately
      }

      // Reset zoom and scroll
      setZoomScale(ZOOM_CONSTRAINTS.min * 5); // This should be 0.1, not 0.1 * 100
      setScrollPosition(0);

      // Reset timeline scroll if timelineRef exists
      if (timelineRef && timelineRef.current) {
        timelineRef.current.scrollLeft = 0;
      }

      // Reset current frame to 0
      setCurrentFrame(0);

      // Dispatch custom event to reset frame display
      window.dispatchEvent(
        new CustomEvent("timeline-frame-update", {
          detail: {
            frame: 0,
            isDragging: false,
            realTime: false,
            dragEnd: false,
            click: true,
            videoEnded: false,
            videoRestart: true,
          },
        })
      );

      // Dispatch new project reset event
      window.dispatchEvent(new CustomEvent("new-project-reset"));

      // Clear Electron-side data
      if (window.electronAPI) {
        // Clear the metadata
        await window.electronAPI.clearProjectMetadata();

        // Reset project state on Electron side (if available)
        if (
          "resetProjectState" in window.electronAPI &&
          typeof window.electronAPI.resetProjectState === "function"
        ) {
          await (window.electronAPI as any).resetProjectState();
        }
      }

      // Clear loading state
      setIsProjectLoading(false);
    } catch (error) {
      console.error("❌ Error starting new project:", error);

      // Clear loading state on error
      setIsProjectLoading(false);
    }
  }, [
    overlays,
    deleteOverlay,
    resetOverlays,
    visibleRows,
    removeRow,
    setZoomScale,
    setScrollPosition,
    timelineRef,
    setCurrentFrame,
    setIsAnalyseVideoDone,
    setIsProjectLoading,
  ]);

  // Ref to always access current overlay state (avoids stale closure capture)
  const overlaysRef = useRef(overlays);
  overlaysRef.current = overlays;

  // Ref to track current visible rows (to avoid stale closure capture)
  const visibleRowsRef = useRef(visibleRows);
  visibleRowsRef.current = visibleRows;

  // Ref to store project data for segment mapping
  const projectDataRef = useRef<any>(null);

  // Sync project data to ref for access in async callbacks
  useEffect(() => {
    projectDataRef.current = projectDataForSplitting;
  }, [projectDataForSplitting]);

  // Function to perform segment name mapping after splits are complete
  const performSegmentMapping = useCallback(async () => {
    const savedSegments = projectDataRef.current.segments.aiVideoAnalytics;
    const segmentKeys = Object.keys(savedSegments).filter((key) =>
      key.startsWith("segment")
    );

    // Get current video overlays from fresh ref
    const currentVideoOverlays = overlaysRef.current
      .filter(
        (overlay) => overlay.type === OverlayType.VIDEO && overlay.row === 0
      )
      .sort((a, b) => a.from - b.from); // Sort by start time

    // Convert time strings to frames
    const timeStringToFrames = (
      timeString: string | number | null | undefined,
      fps: number = 30
    ): number => {
      // Handle non-string inputs
      if (timeString === null || timeString === undefined) {
        return 0;
      }

      // Convert to string if it's a number
      const timeStr = String(timeString);

      if (timeStr === "0") {
        return 0;
      }

      // Validate time string format
      if (typeof timeStr !== "string" || !timeStr.includes(":")) {
        console.warn(
          `⚠️ Invalid time string format: ${timeString}, returning 0`
        );
        return 0;
      }

      const parts = timeStr.split(":");
      if (parts.length !== 3) {
        console.warn(
          `⚠️ Invalid time string format: ${timeString}, expected HH:MM:SS.FF format, returning 0`
        );
        return 0;
      }

      const [hours, minutes, secondsAndFrames] = parts;
      const [seconds, frames = "0"] = secondsAndFrames.split(".");
      return (
        parseInt(hours) * 3600 * fps +
        parseInt(minutes) * 60 * fps +
        parseInt(seconds) * fps +
        parseInt(frames)
      );
    };

    // Map each current segment to its saved counterpart based on time ranges
    for (const segmentKey of segmentKeys) {
      const savedSegment = savedSegments[segmentKey];
      if (!savedSegment || !savedSegment.segmentId) continue;

      const savedStartTime = savedSegment.startTime;
      const savedEndTime = savedSegment.endTime;
      const currentFPS = getEffectiveFPS(overlaysRef.current) || 30;

      const savedStartFrame = timeStringToFrames(savedStartTime, currentFPS);
      const savedEndFrame = timeStringToFrames(savedEndTime, currentFPS);

      // Find the current overlay that matches this time range
      const matchingOverlay = currentVideoOverlays.find((overlay) => {
        const overlayStartFrame = overlay.from;
        const overlayEndFrame = overlay.from + overlay.durationInFrames;

        // Check if the time ranges overlap significantly
        const startMatch = Math.abs(overlayStartFrame - savedStartFrame) <= 2;
        const endMatch = Math.abs(overlayEndFrame - savedEndFrame) <= 2;

        return startMatch && endMatch;
      });

      if (matchingOverlay && matchingOverlay.id !== savedSegment.segmentId) {
        changeOverlay(
          matchingOverlay.id,
          (overlay) =>
            ({
              ...overlay,
              id: savedSegment.segmentId,
              name:
                savedSegment.segmentName || `Segment ${savedSegment.segmentId}`,
            } as any)
        );
      }
    }
  }, [changeOverlay]);

  // Function to automatically remove all gaps
  const removeAllGaps = useCallback(() => {
    try {
      // Get fresh overlay state to avoid stale closure capture
      const currentOverlays = [...overlaysRef.current];

      // Process each row to remove gaps
      for (let rowIndex = 0; rowIndex < visibleRows; rowIndex++) {
        const rowOverlays = currentOverlays
          .filter((overlay) => overlay.row === rowIndex)
          .sort((a, b) => a.from - b.from);

        if (rowOverlays.length === 0) continue;

        // First, handle gap at the beginning (if first overlay doesn't start at 0)
        // But be careful with audio overlays - they should maintain their timing
        if (rowOverlays[0].from > 0) {
          const initialGapSize = rowOverlays[0].from;

          // Skip gap removal for audio rows if they contain sound overlays with specific timing
          const isAudioRow = rowIndex === 1;
          const hasSoundOverlays = rowOverlays.some(
            (overlay) => overlay.type === OverlayType.SOUND
          );

          if (isAudioRow && hasSoundOverlays) {
            console.log(
              `🎵 Skipping initial gap removal for audio row ${rowIndex} - preserving sound overlay timing`
            );
          } else {
            console.log(
              `🔧 Removing initial gap in row ${rowIndex} from 0 to ${rowOverlays[0].from} (size: ${initialGapSize})`
            );

            // Shift all overlays in this row to start from 0
            rowOverlays.forEach((overlay) => {
              const newFrom = overlay.from - initialGapSize;
              changeOverlay(overlay.id, { from: newFrom });
            });
          }
        }

        // Then handle gaps between overlays (process from right to left to avoid position conflicts)
        // But preserve audio overlay timing
        const isAudioRow = rowIndex === 1;
        const hasSoundOverlays = rowOverlays.some(
          (overlay) => overlay.type === OverlayType.SOUND
        );

        if (isAudioRow && hasSoundOverlays) {
          console.log(
            `🎵 Skipping inter-overlay gap removal for audio row ${rowIndex} - preserving sound overlay timing`
          );
        } else {
          for (let i = 0; i < rowOverlays.length - 1; i++) {
            const currentOverlay = rowOverlays[i];
            const nextOverlay = rowOverlays[i + 1];
            const currentEnd =
              currentOverlay.from + currentOverlay.durationInFrames;
            const gapStart = currentEnd;
            const gapEnd = nextOverlay.from;

            if (gapEnd > gapStart) {
              const gapSize = gapEnd - gapStart;
              for (let j = i + 1; j < rowOverlays.length; j++) {
                const overlayToShift = rowOverlays[j];
                const newFrom = overlayToShift.from - gapSize;
                changeOverlay(overlayToShift.id, { from: newFrom });
                overlayToShift.from = newFrom; // Update local copy for next iteration
              }
            }
          }
        }
      }

      // Now that gap removal is complete and overlays state is fresh, apply segment name mapping
      performSegmentMapping();
    } catch (error) {
      console.error("❌ Error in auto gap removal:", error);
    }
  }, [visibleRows, changeOverlay]); // Removed overlays dependency to prevent stale closure

  // Debounced gap removal that waits for state stability
  const debouncedGapRemoval = useCallback(() => {
    const timeoutId = setTimeout(() => {
      removeAllGaps();
    }, 1000); // Wait 1 second for state to stabilize

    return () => clearTimeout(timeoutId);
  }, [removeAllGaps]);

  const handleOpenProject = useCallback(async () => {
    if (!window.electronAPI) {
      alert("This feature is only available in the desktop application.");
      return;
    }

    // Set loading state immediately to prevent gap indicators
    setIsProjectLoading(true);

    try {
      const result = await window.electronAPI.openProject();

      if (!result) {
        // Clear loading state if user cancelled
        setIsProjectLoading(false);
        return;
      }

      const {
        buffer,
        data: projectData,
        filePath: _planckPath,
        audioFiles = {},
      } = result;

      result.data.timeFrameSegment.forEach((cut: any) => {
        addCutTime(cut);
      });
      console.log("Checksss", result.data.timeFrameSegment);

      if (!projectData || !buffer) {
        // Clear loading state on error
        setIsProjectLoading(false);
        return;
      }

      const videoName = projectData.video;
      const dir = projectData.saveDir;

      // Set loading state to hide gap indicators
      setIsProjectLoading(true);

      overlays.forEach((overlay) => {
        deleteOverlay(overlay.id);
      });

      // Reset overlays state
      resetOverlays();

      // Clear segment store
      useSegmentStore.getState().setTimeFrameSegment("");

      // Clear project buffer store
      const projectBufferStore = useProjectBufferStore.getState();
      if (projectBufferStore.videoUrl) {
        URL.revokeObjectURL(projectBufferStore.videoUrl); // Clean up blob URL
      }
      (projectBufferStore.setVideoUrl as any)(null);
      (projectBufferStore.setVideoBuffer as any)(null);

      // Clear data video store
      (useDataVideoStore.getState().setSegment as any)(null);

      // Clear analysis state
      setIsAnalyseVideoDone(false);

      // Remove all rows except the first one (keep removing until we have 1 row)
      let currentRows = visibleRows;
      while (currentRows > 1) {
        removeRow();
        currentRows--; // Manually decrement since visibleRows might not update immediately
      }

      // Reset zoom and scroll
      setZoomScale(ZOOM_CONSTRAINTS.min * 5);
      setScrollPosition(0);

      // Reset timeline scroll if timelineRef exists
      if (timelineRef && timelineRef.current) {
        timelineRef.current.scrollLeft = 0;
      }

      // Reset current frame to 0
      setCurrentFrame(0);

      // Dispatch custom event to reset frame display
      window.dispatchEvent(
        new CustomEvent("timeline-frame-update", {
          detail: {
            frame: 0,
            isDragging: false,
            realTime: false,
            dragEnd: false,
            click: true,
            videoEnded: false,
            videoRestart: true,
          },
        })
      );

      // Clear Electron-side data
      // if (window.electronAPI) {
      //   // Clear the metadata
      //   await window.electronAPI.clearProjectMetadata();

      //   // Reset project state on Electron side (if available)
      //   if (
      //     "resetProjectState" in window.electronAPI &&
      //     typeof window.electronAPI.resetProjectState === "function"
      //   ) {
      //     await (window.electronAPI as any).resetProjectState();
      //   }
      // }

      // Wait for state to fully reset
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // STEP 2: Load video and ensure it starts at frame 0
      try {
        const blob = new Blob([buffer], { type: "video/mp4" });
        const videoUrl = URL.createObjectURL(blob);
        useProjectBufferStore.getState().setVideoUrl(videoUrl);

        const videoFile = new File([blob], videoName, {
          type: "video/mp4",
        });

        await handleFilesAdded([videoFile]);
        await new Promise<void>((resolve) => setTimeout(resolve, 2000));

        // Wait for video overlay to be available in context
        await new Promise<void>((resolve) => setTimeout(resolve, 1000));

        // Get video overlays from fresh context reference
        const currentOverlays = overlaysRef.current;
        const currentVideoOverlays = [...currentOverlays].filter(
          (overlay) => overlay.type === OverlayType.VIDEO
        );

        // Debug context state
        if (currentOverlays.length === 0) {
          console.error(
            "❌ CRITICAL: No overlays found in MenuHandler context after load!"
          );
          console.error("❌ This indicates a context synchronization issue");
          return;
        }

        // Force video overlay to start at frame 0 if it's not
        if (currentVideoOverlays.length > 0) {
          const mainVideo = currentVideoOverlays[0];
          if (mainVideo.from !== 0) {
            changeOverlay(mainVideo.id, { from: 0 });
            await new Promise<void>((resolve) => setTimeout(resolve, 500));
          }
        }
      } catch (videoError: any) {
        console.error("❌ Failed to load video:", videoError);
        throw new Error(`Failed to load video: ${videoError.message}`);
      }

      // Check if there are audio files to determine if we need audio row
      const hasAudioFiles = projectData.segments?.aiVideoAnalytics
        ? Object.keys(projectData.segments.aiVideoAnalytics).some(
            (key) =>
              key.startsWith("segment") &&
              projectData.segments.aiVideoAnalytics[key]?.audioFile
          )
        : false;

      let expectedRows = visibleRows;

      // Only ensure we have 2 rows if there are audio files, otherwise keep 1 row for video only
      const targetRows = hasAudioFiles
        ? Math.max(2, expectedRows)
        : Math.max(1, expectedRows);

      if (expectedRows !== targetRows) {
        if (expectedRows < targetRows) {
          const rowsToAdd = targetRows - expectedRows;
          for (let i = 0; i < rowsToAdd; i++) {
            addRow();
            expectedRows++; // Track the expected row count
            await new Promise((resolve) => setTimeout(resolve, 300));
          }
        } else if (expectedRows > targetRows) {
          const rowsToRemove = expectedRows - targetRows;
          for (let i = 0; i < rowsToRemove; i++) {
            removeRow();
            expectedRows--; // Track the expected row count
            await new Promise((resolve) => setTimeout(resolve, 200));
          }
        }

        // Update the ref with the expected row count
        visibleRowsRef.current = expectedRows;

        // Wait for rows to be properly updated and force re-render
        await new Promise((resolve) => setTimeout(resolve, 1200));
      } else {
        visibleRowsRef.current = expectedRows;
        // Still wait a bit to ensure state stability
        await new Promise((resolve) => setTimeout(resolve, 500));
      }

      // Force verify rows are actually available for audio overlays
      const finalVisibleRows = visibleRowsRef.current;
      if (finalVisibleRows < 2) {
        console.warn(
          `⚠️ WARNING: Only ${finalVisibleRows} rows available, audio overlays on row 1 may not be visible!`
        );
      }

      // STEP 3: Save project metadata for later retrieval
      if (projectData?.segments?.aiVideoAnalytics) {
        try {
          // Store the metadata in electron so getLastSavedMetadata can retrieve it
          if (
            typeof window !== "undefined" &&
            (window as any).electronAPI?.setProjectMetadata
          ) {
            await (window as any).electronAPI.setProjectMetadata({
              projectName: projectData.planckName,
              videoPath: dir,
              savedPath: dir,
              planckPath: _planckPath,
              segments: projectData.segments,
              timeFrameSegments: projectData.timeFrameSegment || [],
            });
          } else {
            // Fallback: store project data globally for access by other components
            (window as any).lastLoadedProjectData = projectData;
          }
        } catch (error) {
          console.error("❌ Error saving project metadata:", error);

          // Even if Electron storage fails, store as fallback
          (window as any).lastLoadedProjectData = projectData;
        }
      } else {
        console.log("⚠️ No segments.aiVideoAnalytics found in project data");
      }

      // STEP 4: Apply splits AFTER video is properly loaded and positioned
      if (projectData?.timeFrameSegment?.length > 0) {
        setProjectDataForSplitting(projectData);

        // Wait for splits to complete
        await new Promise<void>((resolve) => setTimeout(resolve, 2000));

        // Check if splits were applied
        const postSplitOverlays = [...editorContext.overlays];
      }

      // STEP 4: Process audio segments AFTER splits are complete
      if (projectData.segments?.aiVideoAnalytics) {
        const audioSegments = projectData.segments.aiVideoAnalytics;
        const segmentKeys = Object.keys(audioSegments).filter(
          (key) => key.startsWith("segment") && audioSegments[key]?.audioFile
        );

        if (segmentKeys.length > 0) {
          // Wait longer for context to synchronize with all overlays
          await new Promise((resolve) => setTimeout(resolve, 1500));

          // Get fresh context state for overlay count and FPS calculation
          const freshOverlays = [...overlaysRef.current];

          let currentFPS = 30;
          if (freshOverlays.length > 0) {
            try {
              currentFPS = getEffectiveFPS(freshOverlays);
            } catch (fpsError) {
              console.warn(
                "⚠️ Error calculating FPS, using fallback:",
                fpsError
              );
            }
          }

          // Sort segments by start time and assign sequential IDs
          const segmentsWithTiming = segmentKeys
            .map((segmentKey) => {
              const segment = audioSegments[segmentKey];

              // Parse start time to get sorting order
              const parseTimeString = (timeStr: string | number): number => {
                if (typeof timeStr === "number") return timeStr;
                if (typeof timeStr !== "string") return 0;

                const parts = timeStr.split(":");
                if (parts.length !== 3) return 0;

                const hours = parseInt(parts[0], 10);
                const minutes = parseInt(parts[1], 10);
                const seconds = parseFloat(parts[2]);

                return hours * 3600 + minutes * 60 + seconds;
              };

              const startTimeSeconds = parseTimeString(
                segment.startTime || "00:00:00.00"
              );

              return {
                segmentKey,
                segment,
                startTimeSeconds,
              };
            })
            .filter((item) => item.segment?.audioFile) // Only include segments with audio files
            .sort((a, b) => a.startTimeSeconds - b.startTimeSeconds); // Sort by start time

          // Process segments in chronological order using their original segmentId
          for (let index = 0; index < segmentsWithTiming.length; index++) {
            const { segmentKey, segment } = segmentsWithTiming[index];
            const audioFileName = segment.audioFile;

            // Use the original segmentId from the .planck file
            const originalSegmentId = segment.segmentId;

            if (!originalSegmentId) {
              console.warn(`⚠️ Skipping ${segmentKey} - no segmentId found`);
              continue;
            }

            if (!audioFileName) {
              console.warn(
                `⚠️ Skipping ${segmentKey} - no audio file specified`
              );
              continue;
            }

            // Check for undefined or invalid time values
            if (
              segment.startTime === undefined ||
              segment.endTime === undefined
            ) {
              console.warn(
                `⚠️ Skipping ${segmentKey} - missing startTime or endTime`
              );
              continue;
            }

            // Parse time strings (format: "00:00:02.00") to seconds
            const parseTimeString = (timeStr: string | number): number => {
              if (typeof timeStr === "number") return timeStr;

              if (typeof timeStr !== "string") {
                throw new Error(`Invalid time format: ${timeStr}`);
              }

              // Parse format "00:00:02.00" (HH:MM:SS.ss)
              const parts = timeStr.split(":");
              if (parts.length !== 3) {
                throw new Error(`Invalid time format: ${timeStr}`);
              }

              const hours = parseInt(parts[0], 10);
              const minutes = parseInt(parts[1], 10);
              const seconds = parseFloat(parts[2]);

              return hours * 3600 + minutes * 60 + seconds;
            };

            let startTimeSeconds: number;
            let endTimeSeconds: number;

            try {
              startTimeSeconds = parseTimeString(segment.startTime);
              endTimeSeconds = parseTimeString(segment.endTime);
            } catch (timeParseError) {
              continue;
            }

            if (endTimeSeconds <= startTimeSeconds) {
              continue;
            }

            // Build audio URL
            let audioUrl = null;

            if (audioFiles[audioFileName]) {
              audioUrl = audioFiles[audioFileName];
            } else {
              const cleanDir = dir.replace(/\\/g, "/");
              const audioPath = `${cleanDir}/ai/${audioFileName}`;
              audioUrl = `file:///${audioPath}`
                .replace(/\/+/g, "/")
                .replace("file:/", "file:///");
            }

            if (!audioUrl) {
              console.error(
                `❌ Could not construct audio URL for: ${audioFileName}`
              );
              continue;
            }

            try {
              // Load audio file using electronAPI
              try {
                // Convert file:// URL to filesystem path
                const filePath = audioUrl.startsWith("file://")
                  ? decodeURIComponent(audioUrl.substring(8)) // Windows: C:/... -> remove leading slash and decode
                  : audioUrl;

                // Use electronAPI to read the file
                const result = await window.electronAPI.readFileBuffer(
                  filePath
                );
                if (!result) {
                  throw new Error("Failed to read audio file");
                }

                // Type guard to ensure we have a valid buffer
                let buffer: Uint8Array;
                if (result instanceof Uint8Array) {
                  buffer = result;
                } else if (result instanceof ArrayBuffer) {
                  buffer = new Uint8Array(result);
                } else {
                  throw new Error("Invalid buffer type received");
                }

                if (buffer.byteLength === 0) {
                  throw new Error("Empty audio file");
                }

                // Create blob with proper type handling - avoid SharedArrayBuffer check
                let arrayBuffer: ArrayBuffer;

                try {
                  // Check if the buffer is actually an ArrayBuffer
                  if (buffer.buffer instanceof ArrayBuffer) {
                    arrayBuffer = buffer.buffer.slice(
                      buffer.byteOffset,
                      buffer.byteOffset + buffer.byteLength
                    );
                  } else {
                    // Fallback: create new ArrayBuffer and copy data
                    arrayBuffer = new ArrayBuffer(buffer.byteLength);
                    const view = new Uint8Array(arrayBuffer);
                    view.set(buffer);
                  }
                } catch (error) {
                  // Final fallback: create new ArrayBuffer and copy data
                  arrayBuffer = new ArrayBuffer(buffer.byteLength);
                  const view = new Uint8Array(arrayBuffer);
                  view.set(buffer);
                }

                const audioBlob = new Blob([arrayBuffer], {
                  type: "audio/wav",
                });

                // Calculate timing using parsed time values
                const startFrame = Math.max(
                  0,
                  Math.round(startTimeSeconds * currentFPS)
                );
                const endFrame = Math.round(endTimeSeconds * currentFPS);
                const durationFrames = Math.max(1, endFrame - startFrame);

                if (
                  !Number.isFinite(startFrame) ||
                  !Number.isFinite(durationFrames) ||
                  durationFrames <= 0
                ) {
                  console.error(
                    `❌ Invalid frame calculations for ${segmentKey}`
                  );
                  continue;
                }

                // Use the original segmentId from the .planck file as the overlay ID
                const overlayId = originalSegmentId;

                // Create audio overlay object - place on second row (row 1)
                const audioOverlay: SoundOverlay = {
                  id: overlayId,
                  type: OverlayType.SOUND,
                  from: startFrame,
                  durationInFrames: durationFrames,
                  row: 1, // Second row for audio
                  src: URL.createObjectURL(audioBlob),
                  content: audioFileName,
                  height: 50,
                  left: 0,
                  top: 0,
                  width: 100,
                  isDragging: false,
                  rotation: 0,
                  startFromSound: 0,
                  styles: {
                    volume: 1.0,
                    opacity: 1,
                    zIndex: 1,
                  },
                };

                // Debug: Check visible rows before adding audio - use fresh ref value
                const currentVisibleRows = visibleRowsRef.current;

                // Safety check: ensure audio row is available
                if (audioOverlay.row >= currentVisibleRows) {
                  console.error(
                    `❌ CRITICAL: Audio overlay row ${audioOverlay.row} is not visible (only ${currentVisibleRows} rows available)!`
                  );
                  console.error(
                    `❌ This will cause the audio overlay to not be displayed!`
                  );
                  // Force add a row if needed
                  if (currentVisibleRows < 2) {
                    addRow();
                    visibleRowsRef.current = currentVisibleRows + 1;
                    await new Promise((resolve) => setTimeout(resolve, 500));
                  }
                }

                addOverlay(audioOverlay);

                // Wait longer for overlay to be properly added
                await new Promise((resolve) => setTimeout(resolve, 800));

                // Verify using fresh context reference and multiple strategies
                let addedOverlay = overlaysRef.current.find(
                  (o) => o.id === overlayId
                );

                // If not found by exact ID, try finding by content and timing
                if (!addedOverlay) {
                  addedOverlay = overlaysRef.current.find(
                    (o) =>
                      o.type === OverlayType.SOUND &&
                      (o as SoundOverlay).content === audioFileName &&
                      o.row === 1 &&
                      Math.abs(o.from - startFrame) <= 2 // Allow small timing differences
                  );
                }

                if (addedOverlay) {
                  console.log(
                    `✅ Created audio overlay with Original Segment ID ${originalSegmentId} (Overlay ID: ${addedOverlay.id})`
                  );
                } else {
                  overlaysRef.current.forEach((o) => {
                    const contentText =
                      o.type === OverlayType.SOUND
                        ? (o as SoundOverlay).content
                        : o.type === OverlayType.TEXT
                        ? (o as TextOverlay).content
                        : o.type === OverlayType.VIDEO
                        ? (o as ClipOverlay).content
                        : "N/A";
                    console.warn(
                      `  - ${o.type} overlay: ID ${o.id}, content: ${contentText}, row: ${o.row}`
                    );
                  });
                }
              } catch (audioError) {
                console.error(
                  `❌ Failed to process audio file ${audioFileName}:`,
                  audioError
                );
                continue;
              }
            } catch (error) {
              console.error(`❌ Error processing segment:`, error);
              continue;
            }
          } // End of for loop
        } // End of segmentKeys loop
      }

      // Final timeline update to ensure everything is at frame 0
      window.dispatchEvent(
        new CustomEvent("timeline-frame-update", {
          detail: {
            frame: 0,
            isDragging: false,
            realTime: false,
            dragEnd: false,
            click: true,
            videoEnded: false,
            videoRestart: true,
          },
        })
      );

      // Final verification - wait for all async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Final verification of all overlays using fresh ref
      const finalAllOverlays = [...overlaysRef.current];
      const finalVideoOverlays = finalAllOverlays.filter(
        (o) => o.type === OverlayType.VIDEO
      );
      const finalAudioOverlays = finalAllOverlays.filter(
        (o) => o.type === OverlayType.SOUND
      );

      const projectName = projectData.planckName || "Untitled Project";

      if (finalAllOverlays.length === 0) {
        console.error(
          `❌ CRITICAL: ${projectName} loaded but NO OVERLAYS accessible in MenuHandler context!`
        );
        console.error(
          "❌ This confirms context isolation issue between MenuHandler and VideoPlayer"
        );
      }

      // Dispatch project loaded event with project data
      window.dispatchEvent(
        new CustomEvent("project-loaded", {
          detail: projectData,
        })
      );

      // Clear loading state and remove any remaining gaps
      setTimeout(() => {
        setIsProjectLoading(false);

        // Use debounced gap removal for better state synchronization
        debouncedGapRemoval();
      }, 1000);
    } catch (error: unknown) {
      // Proper error typing and handling
      console.error("❌ Error in handleOpenProject:", error);

      // Safe error handling
      if (error instanceof Error) {
        console.error("❌ Error stack:", error.stack);
      }

      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      alert(
        `Failed to open project: ${errorMessage}\n\nPlease check the file format and try again.`
      );

      // Clear loading state on error
      setIsProjectLoading(false);
    } finally {
      // Any cleanup code can go here
    }
  }, [
    overlays,
    deleteOverlay,
    handleFilesAdded,
    addOverlay,
    resetOverlays,
    visibleRows,
    removeRow,
    addRow,
    setCurrentFrame,
    setZoomScale,
    setScrollPosition,
    timelineRef,
    changeOverlay,
    setProjectDataForSplitting,
    setIsAnalyseVideoDone,
    setIsProjectLoading,
    debouncedGapRemoval,
  ]);

  const handleSave = useCallback(async () => {
    const cutTimes = useCutStore.getState().cutTimes;
    await saveVideoProject();

    const buffer = await getLastSavedVideoBuffer();
    if (!buffer) {
      return;
    }

    const metadata = {
      savedAt: new Date().toISOString(),
      timeFrameSegment: cutTimes,
    };

    if (window.electronAPI?.onSaveVideo) {
      const result = await window.electronAPI.onSaveVideo(buffer, metadata);
      if (result.success) {
        handleVideoSaveSuccess(result);
        alert(
          `Video saved to: ${result.filePath}\nMetadata saved to: ${result.metaPath}`
        );
      } else {
        console.log("Save cancelled or failed:", result.message);
      }
    } else {
      console.warn("Electron API saveVideo not available");
    }
  }, [timeFrameSegment]);

  const handleSaveAs = useCallback(async () => {
    await saveVideoProject();

    const buffer = await getLastSavedVideoBuffer();
    if (!buffer) {
      return;
    }

    const metadata = {
      savedAt: new Date().toISOString(),
    };

    if (window.electronAPI?.onSaveAsVideo) {
      const result = await window.electronAPI.onSaveAsVideo(buffer, metadata);
      if (result.success) {
        handleVideoSaveSuccess(result);
        alert(
          `Video saved to: ${result.filePath}\nMetadata saved to: ${result.metaPath}`
        );
      } else {
        console.log("Save cancelled or failed:", result.message);
      }
    } else {
      console.warn("Electron API saveVideo not available");
    }
  }, []);

  const handleImportMarkers = useCallback(() => {
    // Clear any existing XML FPS override to get true video FPS
    const { setXmlFpsOverride } = require("../../utils/fps-utils");
    setXmlFpsOverride(null);

    // Set current video FPS before opening the modal
    overlays.forEach((overlay, index) => {
      if (overlay.type === OverlayType.VIDEO) {
        const clipOverlay = overlay as any;
      }
    });

    const currentFPS = getEffectiveFPS(overlays);
    setCurrentVideoFPS(currentFPS);
    openImportMarkersModal();
  }, [openImportMarkersModal, setCurrentVideoFPS, overlays]);

  const handleExportMarkersModal = useCallback(() => {
    openExportMarkersModal();
  }, [openExportMarkersModal]);

  const handleExportModal = useCallback(() => {
    openExportModal();
  }, [openExportModal]);

  const handleTriggerAutoCut = useCallback(() => {
    // Close any existing popup first (if it exists)
    const BtnClosePopup = document.querySelector(
      ".btn-close-popup"
    ) as HTMLButtonElement;
    if (BtnClosePopup) {
      BtnClosePopup.click();
    }

    // Find and click the Auto Cut button in timeline-controls
    setTimeout(() => {
      const autoCutButton = document.querySelector(
        ".btn-auto-cut"
      ) as HTMLButtonElement;
      if (autoCutButton) {
        autoCutButton.click();
      }
    }, 100);
  }, []);

  const handleTriggerCreateSegment = useCallback(() => {
    // Close any existing popup first (if it exists)
    const BtnClosePopup = document.querySelector(
      ".btn-close-popup"
    ) as HTMLButtonElement;
    if (BtnClosePopup) {
      BtnClosePopup.click();
    }

    // Find and click the Create Segment button in timeline-controls
    setTimeout(() => {
      const BtnCreateSegment = document.querySelector(
        ".btn-create-segment"
      ) as HTMLButtonElement;
      if (BtnCreateSegment) {
        BtnCreateSegment.click();
      }
    }, 100);
  }, []);

  const handleAnalyticsMenuClick = useCallback((data: any) => {
    window.dispatchEvent(
      new CustomEvent("analytics-menu-click", { detail: data })
    );
  }, []);

  // Set up menu event listeners
  useEffect(() => {
    if (!window.electronAPI) return;

    // Register menu event handlers
    const electronAPI = window.electronAPI as any;
    electronAPI.onMenuNewProject(() => {
      newProject();
    });
    electronAPI.onMenuOpenProject(() => {
      handleOpenProject();
    });
    electronAPI.onMenuSave(() => {
      handleSave();
    });
    electronAPI.onMenuSaveAs(() => {
      handleSaveAs();
    });
    electronAPI.onMenuImportMedia(() => {
      console.log("📋 Import media menu clicked");
    });
    electronAPI.onMenuImport(() => {
      handleImportMarkers();
    });
    electronAPI.onMenuExportMarkers(() => {
      handleExportMarkersModal();
    });
    electronAPI.onMenuExport(() => {
      handleExportModal();
    });
    electronAPI.onMenuAddCutMarker(() => {
      handleTriggerAutoCut();
    });
    electronAPI.onMenuAddManualCutMarker(() => {
      handleTriggerCreateSegment();
    });
    electronAPI.onMenuSelectCutMarker(() => {
      console.log("handle Select Cut Marker clicked");
    });

    // Check if analytics menu handler exists, if not, we'll need to add it to electron
    if (typeof electronAPI.onMenuAnalytics === "function") {
      electronAPI.onMenuAnalytics((event: any, data: any) => {
        handleAnalyticsMenuClick(data);
      });
    } else {
      console.warn(
        "electronAPI.onMenuAnalytics not available - menu handler not registered"
      );
    }

    // Cleanup listeners on unmount
    return () => {
      if (
        window.electronAPI &&
        "removeAllMenuListeners" in window.electronAPI
      ) {
        (window.electronAPI as any).removeAllMenuListeners();
      }
    };
  }, [
    newProject,
    handleOpenProject,
    handleSave,
    handleSaveAs,
    handleImportMarkers,
    handleExportMarkersModal,
    handleExportModal,
    handleTriggerAutoCut,
    handleAnalyticsMenuClick,
  ]);

  // Handle project data splitting (from original editor-header logic)
  useEffect(() => {
    const currentOverlays = overlaysRef.current;
    if (projectDataForSplitting && currentOverlays.length > 0) {
      try {
        if (projectDataForSplitting.timeFrameSegment?.length > 0) {
          const currentFPS = getEffectiveFPS(currentOverlays);

          // Create split positions with their corresponding segment IDs
          const splitPositionsWithSegmentIds =
            projectDataForSplitting.timeFrameSegment
              .map((segment: any, index: number) => {
                const {
                  hours = 0,
                  minutes = 0,
                  seconds = 0,
                  frames = 0,
                  fps = currentFPS,
                } = segment;
                const framePosition =
                  hours * 3600 * fps +
                  minutes * 60 * fps +
                  seconds * fps +
                  frames;

                if (!Number.isFinite(framePosition) || framePosition <= 0) {
                  console.warn("⚠️ Invalid split position:", {
                    segment,
                    framePosition,
                  });
                  return null;
                }

                // Find corresponding segment ID from aiVideoAnalytics based on which segment the split creates
                let segmentId = index + 2; // Default fallback (segment 1 is the first part before first split)

                if (projectDataForSplitting.segments?.aiVideoAnalytics) {
                  const aiVideoAnalytics =
                    projectDataForSplitting.segments.aiVideoAnalytics;

                  // Convert frame back to time string for comparison
                  const timeStringToFrames = (
                    timeString: string,
                    fps: number = 30
                  ): number => {
                    if (timeString === "0") return 0;
                    const [hours, minutes, secondsAndFrames] =
                      timeString.split(":");
                    const [seconds, frames = "0"] = secondsAndFrames.split(".");
                    return (
                      parseInt(hours) * 3600 * fps +
                      parseInt(minutes) * 60 * fps +
                      parseInt(seconds) * fps +
                      parseInt(frames)
                    );
                  };

                  // Map split positions to segment IDs based on a predefined mapping
                  // Since timeFrameSegment and aiVideoAnalytics represent different data,
                  // we need to create a logical mapping based on the order
                  const sortedSegments = Object.entries(aiVideoAnalytics)
                    .filter(
                      ([key, segment]) =>
                        key.startsWith("segment") && (segment as any).segmentId
                    )
                    .map(([key, segment]) => ({
                      key,
                      segmentId: (segment as any).segmentId,
                      startTime: (segment as any).startTime,
                      startFrame: timeStringToFrames(
                        (segment as any).startTime,
                        currentFPS
                      ),
                    }))
                    .sort((a, b) => a.startFrame - b.startFrame);

                  // Map splits to segments based on index
                  // First split (frame 45) -> second segment (segment2)
                  // Second split (frame 75) -> third segment (segment3)
                  // Third split (frame 109) -> fourth segment (segment6)
                  if (sortedSegments.length > index + 1) {
                    segmentId = sortedSegments[index + 1].segmentId;
                  } else {
                    console.log(
                      `⚠️ No segment available for split ${index} at frame ${framePosition}, using fallback ID ${segmentId}`
                    );
                  }
                }

                return { framePosition, segmentId };
              })
              .filter((item: any) => item !== null)
              .sort((a: any, b: any) => b.framePosition - a.framePosition); // Sort by frame position

          const splitPositions = splitPositionsWithSegmentIds.map(
            (item: any) => item.framePosition
          );

          if (splitPositions.length > 0) {
            splitPositionsWithSegmentIds.forEach(
              ({ framePosition: splitFrame, segmentId }: any) => {
                const videoOverlay = currentOverlays.find(
                  (overlay: any) =>
                    overlay.type === OverlayType.VIDEO &&
                    overlay.row === 0 &&
                    overlay.from < splitFrame &&
                    overlay.from + overlay.durationInFrames > splitFrame
                );

                if (videoOverlay) {
                  try {
                    // Split the overlay
                    const newOverlayId = splitOverlay(
                      videoOverlay.id,
                      splitFrame
                    ) as any;

                    // ✅ IMPORTANT: After splitting, add segmentId to the new overlay
                    if (newOverlayId) {
                      setTimeout(() => {
                        // Find the segment data for this segmentId
                        const segmentData = Object.values(
                          projectDataForSplitting.segments?.aiVideoAnalytics ||
                            {}
                        ).find((seg: any) => seg.segmentId === segmentId);

                        if (segmentData) {
                          changeOverlay(
                            newOverlayId,
                            (overlay) =>
                              ({
                                ...overlay,
                                segmentId: segmentId, // Store segmentId as metadata
                                segmentName: (segmentData as any).segmentName,
                                name:
                                  (segmentData as any).segmentName ||
                                  `Segment ${segmentId}`,
                              } as any)
                          );
                        }
                      }, 100); // Small delay to ensure overlay is created
                    }
                  } catch (splitError) {
                    console.error(`❌ Split failed:`, splitError);
                  }
                } else {
                  console.warn(
                    `⚠️ No video overlay found at split position ${splitFrame}`
                  );
                }
              }
            );
          } else {
            console.warn("⚠️ No valid split positions found");
          }
        }
      } catch (splitError) {
        console.error("❌ Error processing splits:", splitError);
      }
      // Don't reset projectDataForSplitting to null yet - keep it for segment mapping
    }
  }, [projectDataForSplitting, splitOverlay, changeOverlay]); // Removed overlays dependency

  // This component doesn't render anything - it just handles menu events
  return null;
}
