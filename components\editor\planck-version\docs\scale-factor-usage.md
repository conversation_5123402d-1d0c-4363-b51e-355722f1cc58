# Scale Factor Functionality

The scale factor functionality allows you to dynamically control the size of the video player within its container. This creates a more compact or expanded video container based on user preferences.

## Overview

The scale factor is a value between 0.1 (10%) and 1.0 (100%) that determines how much of the available container space the video player should use. The default value is 0.7 (70%).

## Available Functions

### From `useAspectRatio` Hook

```typescript
const {
  scaleFactor,        // Current scale factor (0.1 to 1.0)
  updateScaleFactor,  // Function to update scale factor
  getScaleFactor,     // Function to get current scale factor
} = useAspectRatio();
```

### From `useEditorContext` Hook

```typescript
const {
  scaleFactor,        // Current scale factor (0.1 to 1.0)
  updateScaleFactor,  // Function to update scale factor
  getScaleFactor,     // Function to get current scale factor
} = useEditorContext();
```

## Usage Examples

### Basic Usage

```typescript
import { useEditorContext } from "../contexts/editor-context";

function VideoSizeControl() {
  const { scaleFactor, updateScaleFactor } = useEditorContext();

  return (
    <div>
      <p>Current size: {Math.round(scaleFactor * 100)}%</p>
      <button onClick={() => updateScaleFactor(0.5)}>Small (50%)</button>
      <button onClick={() => updateScaleFactor(0.7)}>Medium (70%)</button>
      <button onClick={() => updateScaleFactor(1.0)}>Full (100%)</button>
    </div>
  );
}
```

### Slider Control

```typescript
function ScaleSlider() {
  const { scaleFactor, updateScaleFactor } = useEditorContext();

  return (
    <input
      type="range"
      min="0.1"
      max="1.0"
      step="0.05"
      value={scaleFactor}
      onChange={(e) => updateScaleFactor(parseFloat(e.target.value))}
    />
  );
}
```

### Programmatic Control

```typescript
function VideoControls() {
  const { updateScaleFactor, getScaleFactor } = useEditorContext();

  const increaseSize = () => {
    const current = getScaleFactor();
    const newScale = Math.min(1.0, current + 0.1);
    updateScaleFactor(newScale);
  };

  const decreaseSize = () => {
    const current = getScaleFactor();
    const newScale = Math.max(0.1, current - 0.1);
    updateScaleFactor(newScale);
  };

  return (
    <div>
      <button onClick={decreaseSize}>-</button>
      <button onClick={increaseSize}>+</button>
    </div>
  );
}
```

## Pre-built Component

Use the `ScaleFactorControl` component for a complete UI:

```typescript
import { ScaleFactorControl } from "./components/controls/scale-factor-control";

function MyEditor() {
  return (
    <div>
      <ScaleFactorControl />
      {/* Your other editor components */}
    </div>
  );
}
```

## How It Works

1. The scale factor is applied in the `updatePlayerDimensions` function
2. It multiplies the container width and height by the scale factor
3. The video player is then sized to fit within this scaled space
4. This creates the effect of a smaller or larger video container

## Integration Points

- **Video Player**: Automatically uses the scale factor when calculating dimensions
- **Aspect Ratio**: Works with all aspect ratios (16:9, 9:16, 1:1, 4:5)
- **Responsive**: Updates when container size changes
- **Persistent**: Can be saved/loaded with editor state

## Notes

- Scale factor is clamped between 0.1 and 1.0 for safety
- Changes are applied immediately to the video player
- The scale factor affects both width and height proportionally
- Default value is 0.7 (70%) for a compact video container
