import React from "react";

interface ButtonProps {
  children: React.ReactNode;
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  variant?: "danger" | "default";
  customClass?: string;
  disabled?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  children,
  onClick,
  variant = "default",
  customClass = "",
  disabled = false,
}) => {
  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled) {
      e.preventDefault();
      return;
    }
    onClick?.(e);
  };

  const getButtonClasses = (): string => {
    const baseCustomClass = customClass || "";

    if (variant === "danger") {
      return `${baseCustomClass} px-6 py-3 transition-colors rounded-[40px] shadow-[inset_1px_1px_1px_0px_rgba(255,255,255,0.31)] inline-flex justify-center items-center gap-2 ${
        disabled
          ? "bg-red-300 cursor-not-allowed opacity-50"
          : "bg-red-400 hover:bg-red-500/80 cursor-pointer"
      }`;
    } else {
      return `${baseCustomClass} px-6 py-3 transition-colors rounded-[40px] shadow-[inset_1px_1px_1px_0px_rgba(255,255,255,0.25)] outline-[0.50px] backdrop-blur-[2px] inline-flex justify-center items-center ${
        disabled
          ? "bg-neutral-600/50 outline-neutral-500/30 cursor-not-allowed opacity-50"
          : "bg-neutral-400/50 hover:bg-neutral-400/30 outline-white/50 hover:outline-white/30 cursor-pointer"
      }`;
    }
  };

  return (
    <button
      onClick={handleClick}
      className={getButtonClasses()}
      disabled={disabled}
      type="button"
    >
      {children}
    </button>
  );
};

export default Button;