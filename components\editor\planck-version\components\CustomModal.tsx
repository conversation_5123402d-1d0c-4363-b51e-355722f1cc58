import Dialog from "@mui/material/Dialog";
import React from "react";
import Image from "next/image";

interface CustomModalProps {
  open: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

const CustomModal: React.FC<CustomModalProps> = ({ 
  open, 
  onClose, 
  title, 
  children 
}) => {
  return (
    <Dialog
      className="bg-neutral-800/60 backdrop-blur-[6px]"
      onClose={onClose}
      open={open}
      slotProps={{
        paper: {
          style: {
            backgroundColor: "transparent",
            boxShadow: "none",
          },
        },
      }}
    >
      <div className="px-8 py-4 w-[358px] bg-stone-300/40 rounded-2xl shadow-[inset_1px_1px_3px_0px_rgba(255,255,255,0.29)] backdrop-blur-md flex flex-col gap-8">
        <div className="flex justify-between items-center">
          <h3 className="self-stretch justify-start text-white text-xl font-bold font-['Inter'] leading-loose">
            {title}
          </h3>
          <Image
            onClick={onClose}
            src="/icon/close.svg"
            alt="close"
            width={16}
            height={16}
            className="cursor-pointer"
          />
        </div>

        {children}
      </div>
    </Dialog>
  );
};

export default CustomModal;