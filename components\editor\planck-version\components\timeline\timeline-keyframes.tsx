import React from "react";
import { <PERSON><PERSON><PERSON>verlay, ImageOverlay, OverlayType } from "../../types";
import { FPS } from "../../constants";
import { useKeyframes } from "../../hooks/use-keyframes";
import { useVideoAudioWaveform } from "../../hooks/use-video-audio-waveform";
import Image from "next/image";
import { DISABLE_VIDEO_KEYFRAMES } from "../../constants";
import VideoAudioWaveform from "./video-audio-waveform";

/**
 * Props for the TimelineKeyframes component
 */
interface TimelineKeyframesProps {
  /** The overlay object containing video/animation data */
  overlay: ClipOverlay | ImageOverlay;
  /** The current frame number in the timeline */
  currentFrame: number;
  /** Scale factor for timeline zoom level */
  zoomScale: number;
  /** Callback when loading state changes */
  onLoadingChange?: (isLoading: boolean) => void;
  /** Whether the timeline item is selected */
  isSelected?: boolean;
}

/**
 * TimelineKeyframes component displays a timeline of video frames
 * with preview thumbnails at regular intervals.
 *
 * @component
 * @param props.overlay - The overlay object containing video/animation data
 * @param props.currentFrame - The current frame number in the timeline
 * @param props.zoomScale - Scale factor for timeline zoom level
 * @param props.onLoadingChange - Callback when loading state changes
 *
 * @example
 * ```tsx
 * <TimelineKeyframes
 *   overlay={videoOverlay}
 *   currentFrame={30}
 *   zoomScale={1}
 *   onLoadingChange={(isLoading) => console.log('Loading:', isLoading)}
 * />
 * ```
 */
export const TimelineKeyframes: React.FC<TimelineKeyframesProps> = ({
  overlay,
  currentFrame,
  zoomScale,
  onLoadingChange,
  isSelected = false,
}) => {
  const containerRef = React.useRef<HTMLDivElement>(null);
  const { frames, previewFrames, isLoading } = useKeyframes({
    overlay,
    containerRef,
    currentFrame,
    zoomScale,
  });

  // Extract audio waveform for video overlays - DISABLED to remove audio wave display
  // const { waveformData, isLoading: isWaveformLoading } = useVideoAudioWaveform(
  //   overlay.type === OverlayType.VIDEO ? overlay.src : undefined,
  //   overlay.type === OverlayType.VIDEO ? overlay.videoStartTime || 0 : 0,
  //   overlay.durationInFrames || 0,
  //   { numPoints: 50, fps: FPS }
  // );
  const isWaveformLoading = false; // Disabled

  React.useEffect(() => {
    onLoadingChange?.(isLoading || isWaveformLoading);
  }, [isLoading, isWaveformLoading, onLoadingChange]);

  // Calculate thumbnail slots based on zoom level (Veed.io style)
  // Higher zoom = more thumbnails, not larger thumbnails
  const [containerWidth, setContainerWidth] = React.useState(0);

  // Update container width when it changes
  React.useEffect(() => {
    const updateWidth = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.clientWidth);
      }
    };

    updateWidth();

    const resizeObserver = new ResizeObserver(updateWidth);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => resizeObserver.disconnect();
  }, []);

  const calculateThumbnailSlots = React.useMemo(() => {
    if (containerWidth === 0) return 5;

    const baseSlotWidth = 120; // Base width per thumbnail slot
    const minSlots = 3;
    const maxSlots = 20;

    // Calculate slots based on zoom - higher zoom = more slots
    const slots = Math.ceil((containerWidth * zoomScale) / baseSlotWidth);
    return Math.min(Math.max(slots, minSlots), maxSlots);
  }, [containerWidth, zoomScale]);

  const TOTAL_SLOTS = calculateThumbnailSlots;

  // Only show loaded frames
  const loadedFrames = frames.filter(Boolean);

  // Create an array of frames to display, using available frames efficiently
  const displayFrames = Array(TOTAL_SLOTS)
    .fill(null)
    .map((_, index) => {
      if (loadedFrames.length === 0) return null;

      // Distribute available frames across slots
      const frameIndex = Math.floor(
        (index * loadedFrames.length) / TOTAL_SLOTS
      );
      return loadedFrames[Math.min(frameIndex, loadedFrames.length - 1)];
    });

  if (DISABLE_VIDEO_KEYFRAMES) {
    return (
      <div className="flex h-full overflow-hidden w-full bg-pink-100 hover:bg-pink-200 dark:bg-pink-600 dark:hover:bg-pink-500"></div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={`flex h-full overflow-hidden w-full bg-slate-100 dark:bg-gray-900 relative ${isSelected ? 'border border-orange-500' : 'border-2 border-gray-300'}`}
    >
      <div className="flex h-full w-full">
        {displayFrames.map((frame, index) => {
          const previewFrame =
            previewFrames[Math.min(index, previewFrames.length - 1)];
          const isLast = index === TOTAL_SLOTS - 1;
          const timestamp = previewFrame ? Math.floor(previewFrame / FPS) : 0;

          return (
            <div
              key={`${overlay.id}-${index}`}
              className={`relative ${
                !isLast ? "border-r border-slate-300 dark:border-gray-700" : ""
              }`}
              style={{ width: `${100 / TOTAL_SLOTS}%` }}
            >
              {frame && (
                <div className="relative h-full w-full group">
                  <Image
                    src={frame}
                    alt={`Frame at ${timestamp}s`}
                    className="object-cover transition-opacity group-hover:opacity-90"
                    fill
                    sizes={`${100 / TOTAL_SLOTS}vw`}
                    priority={true}
                    quality={85}
                    loading="eager"
                    style={{
                      imageRendering: "crisp-edges",
                      objectFit: "cover",
                    }}
                  />
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Audio waveform overlay for video files - DISABLED to remove audio wave display */}
      {/* {overlay.type === OverlayType.VIDEO && waveformData && (
        <VideoAudioWaveform
          waveformData={waveformData}
          totalSlots={TOTAL_SLOTS}
        />
      )} */}
    </div>
  );
};
