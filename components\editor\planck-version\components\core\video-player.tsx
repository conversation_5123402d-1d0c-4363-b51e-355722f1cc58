/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState, useCallback, useMemo } from "react";
import { Player, PlayerRef } from "@remotion/player";
import { Main } from "../../remotion/main";
import { useEditorContext } from "../../contexts/editor-context";
import { FPS } from "../../constants";
import { Upload, Video, Plus } from "lucide-react";
import VideoSetting from "@/components/editor/planck-version/components/VideoSetting";
import { Overlay, OverlayType } from "../../types";
import Image from "next/image";
import iconCamera from "../icon/camera.svg";
import { useTimeline } from "../../contexts/timeline-context";
import { getEffectiveFPS } from "../../utils/fps-utils";
import VideoSegmentMarkers from "./VideoSegmentMarkers";
interface VideoPlayerProps {
  playerRef: React.RefObject<PlayerRef>;
  onFilesAdded: (files: File[]) => void;
  isProcessing?: boolean;
}

// Supported file types for drag and drop
const ACCEPTED_FILE_TYPES = {
  video: [".mp4", ".mov", ".avi", ".mkv", ".webm", ".m4v"],
  audio: [".mp3", ".wav", ".aac", ".m4a", ".ogg", ".flac"],
  image: [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"],
};

const ALL_ACCEPTED_TYPES = [
  ...ACCEPTED_FILE_TYPES.video,
  ...ACCEPTED_FILE_TYPES.audio,
  ...ACCEPTED_FILE_TYPES.image,
];

/**
 * Validates and fixes durationInFrames to prevent NaN errors
 */
const validateDurationInFrames = (
  duration: number,
  fallback: number = 1500
): number => {
  // Check if duration is a valid number
  if (
    typeof duration !== "number" ||
    !Number.isFinite(duration) ||
    isNaN(duration)
  ) {
    console.warn(
      `⚠️ Invalid durationInFrames: ${duration}, using fallback: ${fallback}`
    );
    return fallback;
  }

  // Ensure duration is positive
  if (duration <= 0) {
    console.warn(
      `⚠️ durationInFrames must be positive: ${duration}, using fallback: ${fallback}`
    );
    return fallback;
  }

  // Ensure duration is an integer
  const rounded = Math.round(duration);
  return rounded;
};

/**
 * Validates and fixes an array of overlays to prevent NaN errors
 */
const validateOverlaysArray = (overlays: Overlay[]): Overlay[] => {
  return overlays.map((overlay) => validateOverlay(overlay));
};

/**
 * Validates and fixes overlay properties to prevent NaN errors
 */
const validateOverlay = (overlay: Overlay): Overlay => {
  const validated = { ...overlay };

  // Fix 'from' property
  if (
    typeof validated.from !== "number" ||
    !Number.isFinite(validated.from) ||
    isNaN(validated.from)
  ) {
    console.warn(
      `⚠️ Invalid 'from' value for overlay ${overlay.id}: ${validated.from}, setting to 0`
    );
    validated.from = 0;
  }

  // Fix 'durationInFrames' property
  if (
    typeof validated.durationInFrames !== "number" ||
    !Number.isFinite(validated.durationInFrames) ||
    isNaN(validated.durationInFrames)
  ) {
    console.warn(
      `⚠️ Invalid 'durationInFrames' value for overlay ${overlay.id}: ${validated.durationInFrames}, setting to 150`
    );
    validated.durationInFrames = 150; // 5 seconds at 30fps
  }

  // Ensure positive values
  if (validated.from < 0) {
    console.warn(
      `⚠️ Negative 'from' value for overlay ${overlay.id}: ${validated.from}, setting to 0`
    );
    validated.from = 0;
  }

  if (validated.durationInFrames <= 0) {
    console.warn(
      `⚠️ Non-positive 'durationInFrames' for overlay ${overlay.id}: ${validated.durationInFrames}, setting to 150`
    );
    validated.durationInFrames = 150;
  }

  // Ensure integers
  validated.from = Math.round(validated.from);
  validated.durationInFrames = Math.round(validated.durationInFrames);

  return validated;
};

/**
 * Empty State Component for when no overlays are present
 */
const EmptyPlayerState: React.FC<{ onFilesAdded: (files: File[]) => void }> = ({
  onFilesAdded,
}) => {
  return (
    <div className="absolute inset-0 flex items-center justify-center rounded-[16px] overflow-hidden bg-gradient-to-bl from-stone-300/25 via-white/20 to-neutral-400/25  shadow-[0px_4px_2px_16px_rgba(225,225,225,0.15)] outline outline-1 outline-white">
      <div className="text-center p-8 max-w-md mx-auto">
        <div className="flex justify-center mb-6">
          <Image
            src={iconCamera}
            alt="Video Icon"
            width={40}
            height={40}
            className="mx-auto "
          />
        </div>

        <div className="w-64 text-center justify-start text-white text-base font-medium leading-snug tracking-tight">
          Drop a video here to start working
        </div>

        <div className="mt-4"></div>
      </div>
    </div>
  );
};

/**
 * VideoPlayer component that renders a responsive video editor with overlay support and drag & drop
 */
export const VideoPlayer: React.FC<VideoPlayerProps> = ({
  playerRef,
  onFilesAdded,
  isProcessing = false,
}) => {
  const {
    overlays,
    setSelectedOverlayId,
    changeOverlay,
    selectedOverlayId,
    aspectRatio,
    playerDimensions,
    updatePlayerDimensions,
    getAspectRatioDimensions,
    durationInFrames,
    currentFrame,
  } = useEditorContext();
  const { addRow, visibleRows } = useTimeline();

  // Add state for tracking when validation fixes duration
  const [durationWasFixed, setDurationWasFixed] = useState(false);
  const [mutedSegments, setMutedSegments] = useState<
    Array<{
      id: number;
      startFrame: number;
      endFrame: number;
    }>
  >([]);

  const handleMutedSegmentsChange = useCallback(
    (
      newMutedSegments: Array<{
        id: number;
        startFrame: number;
        endFrame: number;
      }>
    ) => {
      setMutedSegments(newMutedSegments);
    },
    []
  );

  // Auto-mute original audio immediately when video overlay is detected
  useEffect(() => {
    const videoOverlay = overlays.find(
      (overlay) => overlay.type === OverlayType.VIDEO
    );

    if (videoOverlay) {
      // Mute immediately with large duration, regardless of analysis status
      const LARGE_DURATION = 540000; // 5 hours worth of frames at 30fps - covers any reasonable video
      setMutedSegments([
        {
          id: -1, // Use negative ID to distinguish from user-created segments
          startFrame: 0,
          endFrame: LARGE_DURATION,
        },
      ]);
    } else {
      // Clear muted segments when no video overlay exists
      setMutedSegments([]);
    }
  }, [overlays]); // Only depend on overlays, not durationInFrames

  // Update muted segments duration when video analysis completes
  useEffect(() => {
    const videoOverlay = overlays.find(
      (overlay) => overlay.type === OverlayType.VIDEO
    );

    if (videoOverlay && durationInFrames && durationInFrames > 0) {
      const validDuration = validateDurationInFrames(durationInFrames, 1500);
      setMutedSegments([
        {
          id: -1,
          startFrame: 0,
          endFrame: validDuration,
        },
      ]);
    }
  }, [overlays, durationInFrames]);

  // Validate overlays to prevent NaN errors
  const validatedOverlays = useMemo(() => {
    const validated = validateOverlaysArray(overlays);

    // Filter out overlays with zero or negative duration
    return validated.filter((overlay) => overlay.durationInFrames > 0);
  }, [overlays]);

  // Debug logging for duration
  useEffect(() => {
    const isInvalid =
      isNaN(durationInFrames) || !Number.isFinite(durationInFrames);
    setDurationWasFixed(isInvalid);
  }, [durationInFrames, overlays]);

  // Create segments from overlays - now properly typed
  const segments = useMemo(() => {
    return overlays.map((overlay) => {
      let content = "";
      try {
        if ("content" in overlay && overlay.content) {
          content =
            typeof overlay.content === "string"
              ? overlay.content
              : String(overlay.content);
        } else if ("src" in overlay && (overlay as any).src) {
          content = (overlay as any).src;
        } else {
          content = `${overlay.type} ${overlay.id}`;
        }
      } catch (error) {
        content = `Segment ${overlay.id}`;
      }

      return {
        id: overlay.id,
        name: `Segment ${overlay.id}`,
        start: overlay.from / FPS,
        end: (overlay.from + overlay.durationInFrames) / FPS,
        content,
        type: overlay.type,
        from: overlay.from,
        durationInFrames: overlay.durationInFrames,
        row: overlay.row,
        src: (overlay as any).src || null,
      };
    });
  }, [overlays]);

  const selectedSegment = useMemo(() => {
    if (!selectedOverlayId) return null;

    const overlay = overlays.find((o) => o.id === selectedOverlayId);
    if (!overlay) return null;

    let content = "";
    try {
      if ("content" in overlay && overlay.content) {
        content =
          typeof overlay.content === "string"
            ? overlay.content
            : String(overlay.content);
      } else if ("src" in overlay && (overlay as any).src) {
        content = (overlay as any).src;
      } else {
        content = `${overlay.type} ${overlay.id}`;
      }
    } catch (error) {
      content = `Segment ${overlay.id}`;
    }

    return {
      id: overlay.id,
      name: `Segment ${overlay.id}`,
      start: overlay.from / FPS,
      end: (overlay.from + overlay.durationInFrames) / FPS,
      content,
      type: overlay.type,
      from: overlay.from,
      durationInFrames: overlay.durationInFrames,
      row: overlay.row,
      src: (overlay as any).src || null,
    };
  }, [overlays, selectedOverlayId]);

  // Handle segment updates
  const handleSegmentUpdate = useCallback(
    (updatedSegment: any) => {
      if (updatedSegment?.id) {
        const calculatedDuration = Math.round(
          (updatedSegment.end - updatedSegment.start) * FPS
        );
        const validatedDuration = Math.max(calculatedDuration, 1); // Ensure minimum duration of 1

        changeOverlay(updatedSegment.id, {
          from: Math.round(updatedSegment.start * FPS),
          durationInFrames: validatedDuration,
          // CRITICAL: Pass through any style updates (like volume muting)
          ...(updatedSegment.styles && { styles: updatedSegment.styles }),
        });
      }
    },
    [changeOverlay]
  );

  // Handle segments array updates
  const handleSegmentsChange = useCallback(
    (updatedSegments: any[]) => {
      updatedSegments.forEach((segment) => {
        if (segment?.id) {
          changeOverlay(segment.id, {
            from: Math.round(segment.start * FPS),
            durationInFrames: Math.round((segment.end - segment.start) * FPS),
            // CRITICAL: Pass through any style updates (like volume muting)
            ...(segment.styles && { styles: segment.styles }),
          });
        }
      });
    },
    [changeOverlay]
  );

  // Extract video source
  const videoSrc = useMemo(() => {
    const videoOverlay = overlays.find(
      (overlay) => overlay.type === OverlayType.VIDEO
    );
    return videoOverlay ? (videoOverlay as any).src : null;
  }, [overlays]);

  // Drag and drop state
  const [isDragOver, setIsDragOver] = useState(false);
  const [dragCounter, setDragCounter] = useState(0);

  // Check if player is empty
  const isEmpty = overlays.length === 0;

  // File validation
  const isValidFile = useCallback((file: File): boolean => {
    const extension = "." + file.name.split(".").pop()?.toLowerCase();
    return ALL_ACCEPTED_TYPES.includes(extension);
  }, []);

  // Drag and drop handlers
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragCounter((prev) => prev + 1);
    if (e.dataTransfer.items?.length > 0) {
      setIsDragOver(true);
    }
  }, []);

  const handleDragLeave = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setDragCounter((prev) => prev - 1);
      if (dragCounter <= 1) {
        setIsDragOver(false);
      }
    },
    [dragCounter]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragOver(false);
      setDragCounter(0);

      const files = Array.from(e.dataTransfer.files);
      const validFiles = files.filter(isValidFile);
      if (validFiles.length > 0) {
        onFilesAdded(validFiles);
      }
    },
    [onFilesAdded, isValidFile]
  );

  // Set CSS variables for timeline height and update player dimensions
  useEffect(() => {
    const ROW_HEIGHT = 44; // From constants.ts
    const TIMELINE_CONTROLS_HEIGHT = 65; // Height for timeline controls
    const timelineHeight =
      visibleRows * ROW_HEIGHT * 2 + TIMELINE_CONTROLS_HEIGHT;

    // Set CSS variable for timeline height
    document.documentElement.style.setProperty(
      "--timeline-height",
      `${timelineHeight}px`
    );

    const handleResize = () => {
      const container = document.querySelector(".video-container");
      if (container) {
        const { width } = container.getBoundingClientRect();
        // Calculate height using CSS variable
        const availableHeight = window.innerHeight - timelineHeight;
        updatePlayerDimensions(width, availableHeight);
      }
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [aspectRatio, updatePlayerDimensions, visibleRows]);

  const { width: compositionWidth, height: compositionHeight } =
    getAspectRatioDimensions();

  // FIXED: Validate duration before passing to Player
  const PLAYER_CONFIG = {
    durationInFrames: validateDurationInFrames(durationInFrames, 1500),
    fps: FPS,
  };

  return (
    <div className="w-full h-full overflow-hidden">
      <div
        className={`z-0 video-container relative w-full main-layout ${
          isProcessing ? "opacity-75" : ""
        }`}
        style={{
          height: `${
            isEmpty ? "" : "calc(100vh - var(--timeline-height, 160px))"
          }`,
        }}
      >
        <div
          className={`z-10 absolute inset-2 sm:inset-4 lg:inset-0 flex items-center justify-center rounded-[16px] ${
            isEmpty ? "h-screen" : ""
          }`}
        >
          <div
            className={`flex gap-10 ${
              isEmpty ? "w-full justify-center items-center h-full" : ""
            }`}
          >
            <div
              className={`relative mx-2 sm:mx-0 ${
                isDragOver
                  ? "ring-2 ring-blue-500 ring-inset bg-blue-50/50 dark:bg-blue-900/20"
                  : ""
              }`}
              style={{
                width: isEmpty
                  ? Math.min(playerDimensions.width * 1.3, 1300) // Additional 30% bigger: 1.0 -> 1.3, max 1300px
                  : Math.min(playerDimensions.width, compositionWidth),
                height: isEmpty
                  ? Math.min(playerDimensions.height * 1.17, 900) // Additional 30% bigger: 0.9 -> 1.17, max 900px
                  : Math.min(playerDimensions.height, compositionHeight),
                maxWidth: "100%",
                maxHeight: "100%",
              }}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
            >
              {isEmpty ? (
                <div className="w-full h-full relative player-wrapper flex items-center justify-center">
                  <EmptyPlayerState onFilesAdded={onFilesAdded} />
                </div>
              ) : (
                <>
                  <div className="relative w-full h-full">
                    <Player
                      acknowledgeRemotionLicense
                      ref={playerRef}
                      className="w-full h-full player-wrapper rounded-[16px] !overflow-hidden"
                      component={Main}
                      compositionWidth={compositionWidth}
                      compositionHeight={compositionHeight}
                      style={{ width: "100%", height: "100%" }}
                      durationInFrames={validateDurationInFrames(
                        durationInFrames,
                        1500
                      )}
                      fps={getEffectiveFPS(overlays)}
                      controls={false}
                      loop={false}
                      autoPlay={false}
                      inputProps={{
                        overlays: validatedOverlays,
                        setSelectedOverlayId,
                        changeOverlay,
                        selectedOverlayId,
                        durationInFrames: PLAYER_CONFIG.durationInFrames,
                        fps: FPS,
                        width: compositionWidth,
                        height: compositionHeight,
                        // 🔥 NEW: Pass mutedSegments to Main component
                        mutedSegments: mutedSegments,
                        // videoUrl: videoUrl ?? undefined,
                      }}
                      errorFallback={() => (
                        <div className="w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-[16px]">
                          <p className="text-gray-600 dark:text-gray-400 text-sm">
                            Video rendering error
                          </p>
                        </div>
                      )}
                      overflowVisible
                    />
                    
                    <VideoSegmentMarkers
                      currentFrame={currentFrame}
                      durationInFrames={PLAYER_CONFIG.durationInFrames}
                      overlays={overlays}
                    />
                  </div>
                </>
              )}

              {/* Rest of your drag/drop UI */}
            </div>

            {/* Pass onMutedSegmentsChange callback to VideoSetting */}
            {!isEmpty && (
              <VideoSetting
                videoSrc={videoSrc}
                selectedSegment={selectedSegment}
                onSegmentUpdate={handleSegmentUpdate}
                segments={segments}
                onSegmentsChange={handleSegmentsChange}
                overlays={overlays}
                durationInFrames={PLAYER_CONFIG.durationInFrames}
                currentFrame={currentFrame}
                onFilesAdded={onFilesAdded}
                isProcessing={isProcessing}
                // 🔥 NEW: Pass callback to receive muted segments
                onMutedSegmentsChange={handleMutedSegmentsChange}
                onAddTimelineRow={addRow}
              />
            )}
          </div>
        </div>

        {/* Rest of your processing UI */}
      </div>
    </div>
  );
};
